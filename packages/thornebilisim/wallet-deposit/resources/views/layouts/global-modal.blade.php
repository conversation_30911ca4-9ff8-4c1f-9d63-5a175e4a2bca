@php
    use Illuminate\Support\Facades\DB;
    $queryBuilder = DB::table('cms_pages')
                      ->select('cms_pages.id', 'cms_page_translations.page_title', 'cms_page_translations.url_key', 'cms_page_translations.html_content')
                      ->leftJoin('cms_page_translations', function ($leftJoin) {
                            $leftJoin->on('cms_pages.id', '=', 'cms_page_translations.cms_page_id')
                                ->where('cms_page_translations.locale', app()->getLocale());
                        })->where('cms_pages.is_active', true)->get();
@endphp
@isset($queryBuilder)
    @foreach($queryBuilder AS $key => $value)

        <div
            class="modal micromodal-slide"
            id="modal-{{ $value->url_key }}"
            aria-hidden="false"
            style="z-index: 9999;"
        >
            <div
                class="modal__overlay"
                tabindex="-1"
                data-micromodal-close
            >
                <div
                    class="modal__container"
                    role="dialog"
                    aria-modal="true"
                    aria-labelledby="modal-membershipagreement-title"
                    style="display: block; background-color:#ffffff; max-width:900px;"
                >
                    <header class="modal__header" style="height: 20px; background-color: transparent;">
                        <h2 class="modal__title" id="modal-membershipagreement-title">
                            {{ $value->page_title }}
                        </h2>
                        <button class="modal__close" aria-label="Close modal" data-micromodal-close></button>
                    </header>
                    <main class="modal__content" id="modal-membershipagreement-content">
                        <div
                            id="{{ $value->url_key }}-container"
                            class="bg-white text-justify px-10 pt-10"
                            style="display: block;position: relative;top: 0;padding-top: 0;padding-bottom: 0;"
                        >
                            {!! html_entity_decode( e( $value->html_content) ) !!}
                        </div>
                    </main>
                    <footer class="modal__footer text-center">
                        <button type="button" id="{!! $value->url_key !!}-button" style="display: block;margin: 0 auto;padding: 10px;border: 1px #e5e5e5 solid;background: #222;color: #ffffff;font-size: 18px;font-weight: 700;border-radius: 10px;">
                            <img
                                src="https://launchpad.terramirum.com/images/download.png"
                                class="aspect-video"
                                style="display: flex; float: left !important; max-width: 20px !important; max-height: 20px !important;margin-right: 15px!important;"
                            /> İndir
                        </button>
                    </footer>
                </div>
            </div>
        </div>

    @endforeach
    <link rel="stylesheet" href="/assets/css/modal.css">
    <script src="https://unpkg.com/micromodal/dist/micromodal.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <script>
        @foreach($queryBuilder AS $key => $page)
        $('body').on('click', '#{!! $page->url_key !!}', function () {
            MicroModal.show('modal-{!! $page->url_key !!}', {
                onShow: modal => console.info(`${modal.id} is shown`), // [1]
                //onClose: modal => $('#modal-membershipagreement-content').html(''), // [2]
                openClass: 'is-open', // [5]
                disableScroll: true, // [6]
                disableFocus: false, // [7]
                awaitOpenAnimation: false, // [8]
                awaitCloseAnimation: false, // [9]
                debugMode: true // [10]
            });
        });
        $('body').on('click', '#{!! $page->url_key !!}-button', function generatePDF() {
            console.log('{!! $page->url_key !!}-container');
            const element = document.getElementById('{!! $page->url_key !!}-container');
            var opt = {
                margin: 1,
                filename: '{{ $page->page_title }}.pdf',
                image: {type: 'jpeg', quality: 0.98},
                html2canvas: {scale: 2},
                jsPDF: {unit: 'in', format: 'letter', orientation: 'portrait'}
            };
            html2pdf().set(opt).from(element).save();
        });
        @endforeach

    </script>
@endisset