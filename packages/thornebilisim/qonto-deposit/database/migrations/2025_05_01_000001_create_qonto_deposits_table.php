<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Thorne\QontoDeposit\Enums\Deposit\StatusType;

return new class extends Migration
{
    public function up(): void
    {
        if (! Schema::hasTable('qonto_deposits')) {
            Schema::create('qonto_deposits', function (Blueprint $table) {
                $table->id();
                $table->string('transaction_id')->unique();
                $table->enum('status', StatusType::values())->nullable();
                $table->timestamps();
            });
        }
    }

    public function down(): void
    {
        Schema::dropIfExists('qonto_deposits');
    }
};
