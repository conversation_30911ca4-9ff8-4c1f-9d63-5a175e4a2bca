<footer class="page-footer bg-[#000000] dark:bg-jacarta-900">
    <div class="container">
        <div class="grid grid-cols-12 gap-x-7 gap-y-14 pt-24 pb-12 md:grid-cols-12">
            <div class="col-span-6 sm:col-span-3 md:col-span-4">
                <!-- Logo -->
                <a href="/" class="mb-6 inline-block">
                    <picture>
{{--                        <source srcset="https://thorne.fra1.cdn.digitaloceanspaces.com/milyem/webp/milyem/1000_1000.webp" type="image/webp">--}}
                        <source srcset="/miligold/img/mili-ikon.png" type="image/png">
                        <img src="/miligold/img/mili-ikon.png" alt="Milyem Coin" class="max-h-24 bg-dark rounded-full" />
                    </picture>
                </a>
                <p class="mb-3 dark:text-jacarta-300 text-lg font-bold text-white">
                    {!! __('velocity::app-gold.footer.title') !!} <br>
                    {!! __('velocity::app-gold.footer.subtitle') !!}
                </p>
                <!-- Socials -->
                <div class="flex space-x-5">

                    <a href="#" class="group">
                        <svg
                            aria-hidden="true"
                            focusable="false"
                            data-prefix="fab"
                            data-icon="twitter"
                            class="h-5 w-5 fill-white group-hover:fill-[#D0AA49] dark:group-hover:fill-[#D0AA49]"
                            role="img"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 512 512"
                        >
                            <path
                                d="M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z"
                            ></path>
                        </svg>
                    </a>

                    <a href="#" class="group">
                        <svg
                            aria-hidden="true"
                            focusable="false"
                            data-prefix="fab"
                            data-icon="instagram"
                            class="h-5 w-5 fill-white group-hover:fill-[#D0AA49] dark:group-hover:fill-[#D0AA49]"
                            role="img"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 448 512"
                        >
                            <path
                                d="M224.1 141c-63.6 0-114.9 51.3-114.9 114.9s51.3 114.9 114.9 114.9S339 319.5 339 255.9 287.7 141 224.1 141zm0 189.6c-41.1 0-74.7-33.5-74.7-74.7s33.5-74.7 74.7-74.7 74.7 33.5 74.7 74.7-33.6 74.7-74.7 74.7zm146.4-194.3c0 14.9-12 26.8-26.8 26.8-14.9 0-26.8-12-26.8-26.8s12-26.8 26.8-26.8 26.8 12 26.8 26.8zm76.1 27.2c-1.7-35.9-9.9-67.7-36.2-93.9-26.2-26.2-58-34.4-93.9-36.2-37-2.1-147.9-2.1-184.9 0-35.8 1.7-67.6 9.9-93.9 36.1s-34.4 58-36.2 93.9c-2.1 37-2.1 147.9 0 184.9 1.7 35.9 9.9 67.7 36.2 93.9s58 34.4 93.9 36.2c37 2.1 147.9 2.1 184.9 0 35.9-1.7 67.7-9.9 93.9-36.2 26.2-26.2 34.4-58 36.2-93.9 2.1-37 2.1-147.8 0-184.8zM398.8 388c-7.8 19.6-22.9 34.7-42.6 42.6-29.5 11.7-99.5 9-132.1 9s-102.7 2.6-132.1-9c-19.6-7.8-34.7-22.9-42.6-42.6-11.7-29.5-9-99.5-9-132.1s-2.6-102.7 9-132.1c7.8-19.6 22.9-34.7 42.6-42.6 29.5-11.7 99.5-9 132.1-9s102.7-2.6 132.1 9c19.6 7.8 34.7 22.9 42.6 42.6 11.7 29.5 9 99.5 9 132.1s2.7 102.7-9 132.1z"
                            ></path>
                        </svg>
                    </a>

                </div>
            </div>

            <div class="col-span-6 sm:col-span-3 md:col-span-2 md:col-start-7">
                <h3 class="mb-6 font-display text-lg text-white dark:text-white">{!! __('velocity::app-gold.footer.short_links') !!}</h3>
                <ul class="flex flex-col space-y-1 text-white dark:text-jacarta-300">
                    <li>
                        <a href="/" class="hover:text-[#D0AA49] dark:hover:text-white">{!! __('velocity::app-gold.footer.home') !!}</a>
                    </li>
                    <li>
                        <a href="{{route('shop.home.whats-milyem')}}" class="hover:text-[#D0AA49] dark:hover:text-white">{!! __('velocity::app-gold.footer.whats_milyem') !!}</a>
                    </li>
                    <li>
                        <a href="{{route('shop.home.about-us')}}" class="hover:text-[#D0AA49] dark:hover:text-white">{!! __('velocity::app-gold.footer.about_us') !!}</a>
                    </li>
                    <li>
                        <a href="{{route('shop.home.our-vision')}}" class="hover:text-[#D0AA49] dark:hover:text-white">{!! __('velocity::app-gold.footer.vision') !!}</a>
                    </li>
                    <li>
                        <a href="{{route('shop.home.faq')}}" class="hover:text-[#D0AA49] dark:hover:text-white">{!! __('velocity::app-gold.footer.faq') !!}</a>
                    </li>
                    <li>
                        <a href="{{route('shop.home.contact-us')}}" class="hover:text-[#D0AA49] dark:hover:text-white">{!! __('velocity::app-gold.footer.contact') !!}</a>
                    </li>
                </ul>
            </div>

            <div class="col-span-6 sm:col-span-3 md:col-span-2">
                <h3 class="mb-6 font-display text-lg text-white dark:text-white">{!! __('velocity::app-gold.footer.follow_us') !!}</h3>
                <ul class="flex flex-col space-y-1 text-white dark:text-jacarta-300">
                    <li>
                        <a href="#" class="hover:text-[#D0AA49] dark:hover:text-white">Facebook</a>
                    </li>
                    <li>
                        <a href="#" class="hover:text-[#D0AA49] dark:hover:text-white">İnstagram</a>
                    </li>
                    <li>
                        <a href="#" class="hover:text-[#D0AA49] dark:hover:text-white">Twitter</a>
                    </li>
                    <li>
                        <a href="#" class="hover:text-[#D0AA49] dark:hover:text-white">Linkedin</a>
                    </li>
                    <li>
                        <a href="#" class="hover:text-[#D0AA49] dark:hover:text-white">Telegram</a>
                    </li>
                </ul>
            </div>

            <div class="col-span-6 sm:col-span-3 md:col-span-2">
                <h3 class="mb-6 font-display text-lg text-white dark:text-white">{{ __('velocity::app-static.footer.contracts') }}</h3>
                <ul class="flex flex-col space-y-1 text-white dark:text-jacarta-300">
                    @foreach (getFooterPage() as $item)
                        <li>
                            <a target="_blank" href="/page/{{ $item->translations->where('locale', app()->getLocale())->first()?->url_key }}" class="hover:text-[#D0AA49] dark:hover:text-white">{{ $item->translations->where('locale', app()->getLocale())->first()?->page_title }}</a>
                        </li>
                    @endforeach
                </ul>
            </div>
        </div>
        <div class="flex flex-col items-center justify-between space-y-2 py-8 sm:flex-row sm:space-y-0">
          <span class="text-sm text-white dark:text-white">&copy;
            {{Carbon\Carbon::now()->format('Y')}}
            MiliGold — Made by
            <a href="https://thornebilisim.com" class="text-white hover:text-white">Thorne Bilişim</a>
          </span>

        </div>
    </div>
</footer>