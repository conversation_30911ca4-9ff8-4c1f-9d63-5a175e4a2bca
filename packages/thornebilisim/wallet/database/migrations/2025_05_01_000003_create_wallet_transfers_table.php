<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Thorne\Wallet\Enums\TransferStatus;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('wallet_transfers', function (Blueprint $table) {
            $table->id();
            $table->string('reference', 100)->unique();
            $table->unsignedBigInteger('from_customer_id');
            $table->unsignedBigInteger('to_customer_id');
            $table->string('from_account_number', 11);
            $table->string('to_account_number', 11);
            $table->string('currency', 10);
            $table->decimal('amount', 20, 8);
            $table->decimal('fee', 20, 8)->default('0.********');
            $table->decimal('total_amount', 20, 8);
            $table->enum('status', TransferStatus::values())->nullable();
            $table->text('description')->nullable();
            $table->json('metadata')->nullable();
            $table->timestamp('processed_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->text('failure_reason')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['reference']);
            $table->index(['from_customer_id']);
            $table->index(['to_customer_id']);
            $table->index(['from_account_number']);
            $table->index(['to_account_number']);
            $table->index(['currency']);
            $table->index(['status']);
            $table->index(['created_at']);
            $table->index(['processed_at']);

            $table->index(['from_customer_id', 'status']);
            $table->index(['to_customer_id', 'status']);
            $table->index(['currency', 'status']);

            $table->foreign('from_customer_id')->references('id')->on('customers')->onDelete('restrict');
            $table->foreign('to_customer_id')->references('id')->on('customers')->onDelete('restrict');
            $table->foreign('from_account_number')->references('account_number')->on('wallet_accounts')->onDelete('restrict');
            $table->foreign('to_account_number')->references('account_number')->on('wallet_accounts')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('wallet_transfers');
    }
};
