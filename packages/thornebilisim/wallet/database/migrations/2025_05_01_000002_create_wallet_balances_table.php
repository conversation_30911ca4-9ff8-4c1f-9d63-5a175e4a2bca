<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('wallet_balances', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id');
            $table->string('currency', 10);
            $table->decimal('balance', 20, 8)->default('0.00000000');
            $table->decimal('locked_balance', 20, 8)->default('0.00000000');
            $table->timestamp('web3_synced_at')->nullable();
            $table->json('web3_data')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['customer_id', 'currency']);
            $table->index(['currency']);
            $table->index(['web3_synced_at']);

            $table->unique(['customer_id', 'currency']);

            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('restrict');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('wallet_balances');
    }
};
