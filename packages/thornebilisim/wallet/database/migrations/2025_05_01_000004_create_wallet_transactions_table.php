<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('wallet_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('customer_id');
            $table->string('account_number', 11)->nullable();
            $table->string('reference', 100)->unique();
            $table->enum('type', ['deposit', 'withdrawal', 'transfer', 'fee', 'refund', 'adjustment']);
            $table->enum('direction', ['inbound', 'outbound']);
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled', 'expired']);
            $table->string('method', 50);
            $table->string('currency', 10);
            $table->decimal('amount', 20, 8);
            $table->decimal('fee', 20, 8)->default('0.********');
            $table->decimal('total_amount', 20, 8);
            $table->text('description')->nullable();
            $table->json('metadata')->nullable();
            $table->string('external_reference', 255)->nullable();
            $table->string('external_transaction_id', 255)->nullable();
            $table->timestamp('processed_at')->nullable();
            $table->timestamp('failed_at')->nullable();
            $table->text('failure_reason')->nullable();
            $table->unsignedBigInteger('parent_id')->nullable();
            $table->unsignedBigInteger('transfer_id')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->index(['customer_id']);
            $table->index(['account_number']);
            $table->index(['reference']);
            $table->index(['type']);
            $table->index(['status']);
            $table->index(['method']);
            $table->index(['currency']);
            $table->index(['direction']);
            $table->index(['created_at']);
            $table->index(['processed_at']);
            $table->index(['parent_id']);
            $table->index(['transfer_id']);
            $table->index(['external_reference']);
            $table->index(['external_transaction_id']);

            $table->index(['customer_id', 'currency', 'status']);
            $table->index(['customer_id', 'type', 'status']);
            $table->index(['method', 'status']);

            $table->foreign('customer_id')->references('id')->on('customers')->onDelete('restrict');
            $table->foreign('account_number')->references('account_number')->on('wallet_accounts')->onDelete('restrict');
            $table->foreign('parent_id')->references('id')->on('wallet_transactions')->onDelete('set null');
            $table->foreign('transfer_id')->references('id')->on('wallet_transfers')->onDelete('set null');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('wallet_transactions');
    }
};
