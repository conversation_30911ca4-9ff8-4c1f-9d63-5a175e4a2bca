<?php

use Illuminate\Support\Facades\Route;
use Thorne\Wallet\Http\Controllers\Api\DepositController as ApiDepositController;
use <PERSON>\Wallet\Http\Controllers\Api\PermissionController as ApiPermissionController;
use <PERSON>\Wallet\Http\Controllers\Api\TransferController as ApiTransferController;
use Thorne\Wallet\Http\Controllers\Api\WalletController as ApiWalletController;
use Thorne\Wallet\Http\Controllers\Api\WithdrawController as ApiWithdrawController;

/*
|--------------------------------------------------------------------------
| Wallet API Routes
|--------------------------------------------------------------------------
|
| Here are the API routes for the wallet package. These routes are loaded
| by the WalletServiceProvider and will be assigned to the "api" middleware group.
|
*/

Route::middleware(['api', 'wallet.exceptions'])
    ->prefix('api/wallet')
    ->name('api.wallet.')
    ->group(function () {
        Route::get('/currencies', [ApiWalletController::class, 'currencies'])->name('currencies');
        Route::get('/methods', [ApiWalletController::class, 'methods'])->name('methods');
        Route::post('/validate-account', [ApiWalletController::class, 'validateAccount'])->name('validate-account');

        Route::middleware(['security-layer.session', 'wallet.customer'])->group(function () {
            Route::get('/summary', [ApiWalletController::class, 'summary'])->name('summary.show');

            Route::get('/balances', [ApiWalletController::class, 'showBalance'])->name('balances.show');
            Route::post('/balances/sync', [ApiWalletController::class, 'syncBalance'])->name('balances.sync');
            Route::post('/balances/sync-all', [ApiWalletController::class, 'syncAllBalances'])->name('balances.sync-all');
            Route::get('/balances/compare', [ApiWalletController::class, 'compareBalance'])->name('balances.compare');

            Route::get('/accounts', [ApiWalletController::class, 'indexAccounts'])->name('accounts.index');
            Route::post('/accounts', [ApiWalletController::class, 'storeAccount'])->name('accounts.store');

            Route::get('/transactions', [ApiWalletController::class, 'indexTransactions'])->name('transactions.index');
            Route::get('/transactions/{transaction}', [ApiWalletController::class, 'showTransaction'])->name('transactions.show');

            Route::prefix('deposits')->name('deposits.')->group(function () {
                Route::get('/', [ApiDepositController::class, 'index'])->name('index');
                Route::post('/', [ApiDepositController::class, 'store'])->name('store');
                Route::get('/methods', [ApiDepositController::class, 'methods'])->name('methods.index');
                Route::prefix('company-accounts')->name('company-accounts.')->group(function () {
                    Route::get('/', [ApiDepositController::class, 'companyAccounts'])->name('index');
                    Route::get('/{account}', [ApiDepositController::class, 'showAccount'])->name('show');
                    Route::get('/method/{method}', [ApiDepositController::class, 'accountsByMethod'])->name('by-method');
                    Route::get('/method/{method}/currency/{currency_code}', [ApiDepositController::class, 'accountsByMethodCurrency'])->name('by-method-currency');
                });
                Route::get('/{transaction}', [ApiDepositController::class, 'show'])->name('show');
            });

            Route::prefix('withdrawals')->name('withdrawals.')->group(function () {
                Route::get('/', [ApiWithdrawController::class, 'index'])->name('index');
                Route::post('/', [ApiWithdrawController::class, 'store'])->name('store');
                Route::get('/methods', [ApiWithdrawController::class, 'methods'])->name('methods.index');
                Route::get('/{transaction}', [ApiWithdrawController::class, 'show'])->name('show');
            });

            Route::prefix('transfers')->name('transfers.')->group(function () {
                Route::get('/', [ApiTransferController::class, 'index'])->name('index');
                Route::post('/', [ApiTransferController::class, 'store'])->name('store');
                Route::get('/validate-account', [ApiTransferController::class, 'validateAccount'])->name('validate-account');
                Route::get('/{transfer}', [ApiTransferController::class, 'show'])->name('show');
                Route::post('/{transfer}/cancel', [ApiTransferController::class, 'cancel'])->name('cancel');
            });

            Route::prefix('permissions')->name('permissions.')->group(function () {
                Route::get('/summary', [ApiPermissionController::class, 'summary'])->name('summary');
                Route::get('/limits', [ApiPermissionController::class, 'limits'])->name('limits.index');
                Route::get('/limits/{operation}', [ApiPermissionController::class, 'showLimit'])->name('limits.show');
                Route::get('/usage-statistics', [ApiPermissionController::class, 'usageStatistics'])->name('usage-statistics');
                Route::post('/validate', [ApiPermissionController::class, 'validateOperation'])->name('validate');
            });
        });
    });
