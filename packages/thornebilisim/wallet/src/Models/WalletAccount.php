<?php

namespace Thorne\Wallet\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Log;
use InvalidArgumentException;
use OwenIt\Auditing\Contracts\Auditable;
use Thorne\Wallet\Exceptions\WalletAccountException;
use Thorne\Wallet\Models\Traits\HasWalletAudit;
use Thorne\Wallet\Services\LuhnService;
use Webkul\Customer\Models\Customer;

class WalletAccount extends Model implements Auditable
{
    use HasFactory, HasWalletAudit, SoftDeletes;

    protected $table = 'wallet_accounts';

    protected $fillable = [
        'customer_id',
        'currency',
        'account_number',
        'is_active',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'is_active'  => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(WalletTransaction::class, 'account_number', 'account_number');
    }

    public function incomingTransfers(): HasMany
    {
        return $this->hasMany(WalletTransfer::class, 'to_account_number', 'account_number');
    }

    public function outgoingTransfers(): HasMany
    {
        return $this->hasMany(WalletTransfer::class, 'from_account_number', 'account_number');
    }

    public function balance()
    {
        return WalletBalance::where('customer_id', $this->customer_id)
            ->where('currency', $this->currency)
            ->first();
    }

    public function getBalanceAttribute()
    {
        return $this->balance();
    }

    public static function generateAccountNumber(string $currency, int $customerId): string
    {
        $luhnService    = app(LuhnService::class);
        $currencyConfig = wallet_currency_config($currency);

        if (empty($currencyConfig)) {
            throw new InvalidArgumentException("Unsupported currency: {$currency}");
        }

        $isoCode     = $currencyConfig['iso_code'];
        $maxAttempts = wallet_config('account_number.max_generation_attempts', 100);

        for ($attempt = 1; $attempt <= $maxAttempts; $attempt++) {
            $accountNumber = $luhnService->generateAccountNumber($isoCode);

            if (! static::where('account_number', $accountNumber)->exists()) {
                return $accountNumber;
            }
        }

        Log::critical('Failed to generate unique account number', [
            'currency'    => $currency,
            'customer_id' => $customerId,
            'attempts'    => $maxAttempts,
            'iso_code'    => $isoCode,
        ]);

        throw WalletAccountException::generationFailed($currency, $maxAttempts);
    }

    public static function validateAccountNumber(string $accountNumber, ?string $expectedCurrency = null): bool
    {
        $luhnService = app(LuhnService::class);

        if (! $luhnService->validateAccountNumber($accountNumber)) {
            return false;
        }

        if ($expectedCurrency !== null) {
            $currencyConfig = wallet_currency_config($expectedCurrency);
            if (empty($currencyConfig)) {
                return false;
            }

            $expectedIsoCode = $currencyConfig['iso_code'];
            $actualIsoCode   = $luhnService->extractCurrencyCode($accountNumber);

            return $actualIsoCode === $expectedIsoCode;
        }

        return true;
    }

    public static function getCurrencyFromAccountNumber(string $accountNumber): ?string
    {
        $luhnService = app(LuhnService::class);
        $isoCode     = $luhnService->extractCurrencyCode($accountNumber);

        if ($isoCode === null) {
            return null;
        }

        $currencies = wallet_config('currencies', []);

        foreach ($currencies as $currency => $config) {
            if ($config['iso_code'] === $isoCode) {
                return $currency;
            }
        }

        return null;
    }

    public static function findByAccountNumber(string $accountNumber): ?static
    {
        return static::where('account_number', $accountNumber)
            ->where('is_active', true)
            ->first();
    }

    public static function getOrCreateForCustomer(int $customerId, string $currency): static
    {
        $existingAccount = static::where('customer_id', $customerId)
            ->where('currency', $currency)
            ->where('is_active', true)
            ->first();

        if ($existingAccount) {
            return $existingAccount;
        }

        $accountNumber = static::generateAccountNumber($currency, $customerId);

        return static::create([
            'customer_id'    => $customerId,
            'currency'       => $currency,
            'account_number' => $accountNumber,
            'is_active'      => true,
        ]);
    }

    public function deactivate(): bool
    {
        return $this->update(['is_active' => false]);
    }

    public function activate(): bool
    {
        return $this->update(['is_active' => true]);
    }

    public function isActive(): bool
    {
        return $this->is_active === true;
    }

    public function getFormattedAccountNumber(): string
    {
        $number = $this->account_number;

        return substr($number, 0, 3).'-'.substr($number, 3, 1).'-'.substr($number, 4);
    }

    public function scopeForCurrency($query, string $currency)
    {
        return $query->where('currency', $currency);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
