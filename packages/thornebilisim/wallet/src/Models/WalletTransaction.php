<?php

namespace Thorne\Wallet\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;
use Thorne\Wallet\Enums\TransactionDirection;
use Thorne\Wallet\Enums\TransactionStatus;
use Thorne\Wallet\Enums\TransactionType;
use Thorne\Wallet\Models\Traits\HasWalletAudit;
use Webkul\Customer\Models\Customer;
use OwenIt\Auditing\Contracts\Auditable;

class WalletTransaction extends Model implements Auditable
{
    use HasFactory, HasWalletAudit, SoftDeletes;

    protected $table = 'wallet_transactions';

    protected $fillable = [
        'customer_id',
        'account_number',
        'reference',
        'type',
        'direction',
        'status',
        'method',
        'currency',
        'amount',
        'fee',
        'total_amount',
        'description',
        'metadata',
        'external_reference',
        'external_transaction_id',
        'processed_at',
        'failed_at',
        'failure_reason',
        'parent_id',
        'transfer_id',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'type'          => TransactionType::class,
        'direction'     => TransactionDirection::class,
        'status'        => TransactionStatus::class,
        'amount'        => 'decimal:8',
        'fee'           => 'decimal:8',
        'total_amount'  => 'decimal:8',
        'metadata'      => 'array',
        'processed_at'  => 'datetime',
        'failed_at'     => 'datetime',
        'created_at'    => 'datetime',
        'updated_at'    => 'datetime',
        'deleted_at'    => 'datetime',
    ];

    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(WalletAccount::class, 'account_number', 'account_number');
    }

    public function parent(): BelongsTo
    {
        return $this->belongsTo(self::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(self::class, 'parent_id');
    }

    public function transfer(): BelongsTo
    {
        return $this->belongsTo(WalletTransfer::class, 'transfer_id');
    }

    public static function generateReference(string $type = 'TXN'): string
    {
        return wallet_generate_reference($type);
    }

    public static function createDeposit(array $data): static
    {
        $reference = $data['reference'] ?? static::generateReference('DEP');

        return static::create([
            'customer_id'             => $data['customer_id'],
            'account_number'          => $data['account_number'] ?? null,
            'reference'               => $reference,
            'type'                    => TransactionType::DEPOSIT,
            'direction'               => TransactionDirection::INBOUND,
            'status'                  => $data['status'] ?? TransactionStatus::PENDING,
            'method'                  => $data['method'],
            'currency'                => $data['currency'],
            'amount'                  => $data['amount'],
            'fee'                     => $data['fee']                     ?? '0.********',
            'total_amount'            => $data['total_amount']            ?? $data['amount'],
            'description'             => $data['description']             ?? null,
            'metadata'                => $data['metadata']                ?? [],
            'external_reference'      => $data['external_reference']      ?? null,
            'external_transaction_id' => $data['external_transaction_id'] ?? null,
        ]);
    }

    public static function createWithdrawal(array $data): static
    {
        $reference = $data['reference'] ?? static::generateReference('WTH');

        return static::create([
            'customer_id'             => $data['customer_id'],
            'account_number'          => $data['account_number'] ?? null,
            'reference'               => $reference,
            'type'                    => TransactionType::WITHDRAWAL,
            'direction'               => TransactionDirection::OUTBOUND,
            'status'                  => $data['status'] ?? TransactionStatus::PENDING,
            'method'                  => $data['method'],
            'currency'                => $data['currency'],
            'amount'                  => $data['amount'],
            'fee'                     => $data['fee']                     ?? '0.********',
            'total_amount'            => $data['total_amount']            ?? $data['amount'],
            'description'             => $data['description']             ?? null,
            'metadata'                => $data['metadata']                ?? [],
            'external_reference'      => $data['external_reference']      ?? null,
            'external_transaction_id' => $data['external_transaction_id'] ?? null,
        ]);
    }

    public static function createTransfer(array $data): static
    {
        $reference = $data['reference'] ?? static::generateReference('TRF');

        return static::create([
            'customer_id'    => $data['customer_id'],
            'account_number' => $data['account_number'] ?? null,
            'reference'      => $reference,
            'type'           => TransactionType::TRANSFER,
            'direction'      => $data['direction'],
            'status'         => $data['status'] ?? TransactionStatus::PENDING,
            'method'         => 'internal_transfer',
            'currency'       => $data['currency'],
            'amount'         => $data['amount'],
            'fee'            => $data['fee']          ?? '0.********',
            'total_amount'   => $data['total_amount'] ?? $data['amount'],
            'description'    => $data['description']  ?? null,
            'metadata'       => $data['metadata']     ?? [],
            'parent_id'      => $data['parent_id']    ?? null,
            'transfer_id'    => $data['transfer_id']  ?? null,
        ]);
    }

    public function markAsCompleted(array $data = []): bool
    {
        return $this->update([
            'status'                  => TransactionStatus::COMPLETED,
            'processed_at'            => now(),
            'external_transaction_id' => $data['external_transaction_id'] ?? $this->external_transaction_id,
            'metadata'                => array_merge($this->metadata ?? [], $data['metadata'] ?? []),
        ]);
    }

    public function markAsFailed(string $reason, array $data = []): bool
    {
        return $this->update([
            'status'         => TransactionStatus::FAILED,
            'failed_at'      => now(),
            'failure_reason' => $reason,
            'metadata'       => array_merge($this->metadata ?? [], $data['metadata'] ?? []),
        ]);
    }

    public function markAsCancelled(?string $reason = null): bool
    {
        return $this->update([
            'status'         => TransactionStatus::CANCELLED,
            'failure_reason' => $reason,
        ]);
    }

    public function isPending(): bool
    {
        return $this->status === TransactionStatus::PENDING;
    }

    public function isCompleted(): bool
    {
        return $this->status === TransactionStatus::COMPLETED;
    }

    public function isFailed(): bool
    {
        return $this->status === TransactionStatus::FAILED;
    }

    public function isCancelled(): bool
    {
        return $this->status === TransactionStatus::CANCELLED;
    }

    public function getFormattedAmount(): string
    {
        return wallet_format_amount($this->amount, $this->currency);
    }

    public function getFormattedTotalAmount(): string
    {
        return wallet_format_amount($this->total_amount, $this->currency);
    }

    public function getFormattedFee(): string
    {
        return wallet_format_amount($this->fee, $this->currency);
    }

    public function getAge(): string
    {
        return $this->created_at->diffForHumans();
    }

    public function scopeOfType($query, TransactionType $type)
    {
        return $query->where('type', $type);
    }

    public function scopeWithStatus($query, TransactionStatus $status)
    {
        return $query->where('status', $status);
    }

    public function scopeForCurrency($query, string $currency)
    {
        return $query->where('currency', $currency);
    }

    public function scopeByMethod($query, string $method)
    {
        return $query->where('method', $method);
    }

    public function scopePending($query)
    {
        return $query->where('status', TransactionStatus::PENDING);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', TransactionStatus::COMPLETED);
    }

    public function scopeFailed($query)
    {
        return $query->where('status', TransactionStatus::FAILED);
    }

    public function scopeInDateRange($query, Carbon $from, Carbon $to)
    {
        return $query->whereBetween('created_at', [$from, $to]);
    }
}
