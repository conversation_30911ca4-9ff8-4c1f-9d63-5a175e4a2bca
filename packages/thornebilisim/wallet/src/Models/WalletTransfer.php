<?php

namespace Thorne\Wallet\Models;

use Exception;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use OwenIt\Auditing\Contracts\Auditable;
use Thorne\Wallet\Exceptions\WalletOperationException;
use Thorne\Wallet\Enums\TransactionDirection;
use Thorne\Wallet\Enums\TransactionStatus;
use Thorne\Wallet\Enums\TransferStatus;
use Thorne\Wallet\Models\Traits\HasWalletAudit;
use Webkul\Customer\Models\Customer;

class WalletTransfer extends Model implements Auditable
{
    use HasFactory, HasWalletAudit, SoftDeletes;

    protected $table = 'wallet_transfers';

    protected $fillable = [
        'reference',
        'from_customer_id',
        'to_customer_id',
        'from_account_number',
        'to_account_number',
        'currency',
        'amount',
        'fee',
        'total_amount',
        'status',
        'description',
        'metadata',
        'processed_at',
        'failed_at',
        'failure_reason',
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'status'       => TransferStatus::class,
        'amount'       => 'decimal:8',
        'fee'          => 'decimal:8',
        'total_amount' => 'decimal:8',
        'metadata'     => 'array',
        'processed_at' => 'datetime',
        'failed_at'    => 'datetime',
        'created_at'   => 'datetime',
        'updated_at'   => 'datetime',
        'deleted_at'   => 'datetime',
    ];

    public function fromCustomer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'from_customer_id');
    }

    public function toCustomer(): BelongsTo
    {
        return $this->belongsTo(Customer::class, 'to_customer_id');
    }

    public function fromAccount(): BelongsTo
    {
        return $this->belongsTo(WalletAccount::class, 'from_account_number', 'account_number');
    }

    public function toAccount(): BelongsTo
    {
        return $this->belongsTo(WalletAccount::class, 'to_account_number', 'account_number');
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(WalletTransaction::class, 'reference', 'reference');
    }

    public function outboundTransaction(): BelongsTo
    {
        return $this->belongsTo(WalletTransaction::class, 'reference', 'reference')
            ->where('direction', TransactionDirection::OUTBOUND);
    }

    public function inboundTransaction(): BelongsTo
    {
        return $this->belongsTo(WalletTransaction::class, 'reference', 'reference')
            ->where('direction', TransactionDirection::INBOUND);
    }

    public static function createTransfer(array $data): static
    {
        $reference = $data['reference'] ?? WalletTransaction::generateReference('TRF');

        $fromAccount = WalletAccount::findByAccountNumber($data['from_account_number']);
        $toAccount   = WalletAccount::findByAccountNumber($data['to_account_number']);

        if (! $fromAccount || ! $toAccount) {
            throw WalletOperationException::invalidAccountNumber(
                $fromAccount ? $data['to_account_number'] : $data['from_account_number'],
                'Account not found'
            );
        }

        if ($fromAccount->currency !== $toAccount->currency) {
            throw WalletOperationException::currencyMismatch($fromAccount->currency, $toAccount->currency);
        }

        if ($data['from_customer_id'] === $data['to_customer_id']) {
            throw WalletOperationException::sameAccountOperation($data['from_account_number']);
        }

        DB::beginTransaction();

        try {
            $transfer = static::create([
                'reference'           => $reference,
                'from_customer_id'    => $data['from_customer_id'],
                'to_customer_id'      => $data['to_customer_id'],
                'from_account_number' => $data['from_account_number'],
                'to_account_number'   => $data['to_account_number'],
                'currency'            => $fromAccount->currency,
                'amount'              => $data['amount'],
                'fee'                 => $data['fee'] ?? '0.********',
                'total_amount'        => bcadd($data['amount'], $data['fee'] ?? '0.********', 8),
                'status'              => TransferStatus::PENDING,
                'description'         => $data['description'] ?? null,
                'metadata'            => $data['metadata']    ?? [],
            ]);

            $outboundReference = WalletTransaction::generateReference('TRF');
            $inboundReference  = WalletTransaction::generateReference('TRF');

            WalletTransaction::createTransfer([
                'customer_id'    => $data['from_customer_id'],
                'account_number' => $data['from_account_number'],
                'reference'      => $outboundReference,
                'direction'      => TransactionDirection::OUTBOUND,
                'currency'       => $fromAccount->currency,
                'amount'         => $transfer->amount,
                'fee'            => $transfer->fee,
                'total_amount'   => $transfer->total_amount,
                'description'    => $data['description']  ?? "Transfer to {$data['to_account_number']}",
                'metadata'       => array_merge($data['metadata'] ?? [], [
                    'transfer_type'      => 'outbound',
                    'recipient_account'  => $data['to_account_number'],
                    'transfer_semantics' => 'sender_pays_fee',
                ]),
                'transfer_id' => $transfer->id,
            ]);

            WalletTransaction::createTransfer([
                'customer_id'    => $data['to_customer_id'],
                'account_number' => $data['to_account_number'],
                'reference'      => $inboundReference,
                'direction'      => TransactionDirection::INBOUND,
                'currency'       => $toAccount->currency,
                'amount'         => $transfer->amount,
                'fee'            => '0.********',
                'total_amount'   => $transfer->amount,
                'description'    => $data['description'] ?? "Transfer from {$data['from_account_number']}",
                'metadata'       => array_merge($data['metadata'] ?? [], [
                    'transfer_type'      => 'inbound',
                    'sender_account'     => $data['from_account_number'],
                    'transfer_semantics' => 'recipient_receives_full_amount',
                ]),
                'transfer_id' => $transfer->id,
            ]);

            DB::commit();

            return $transfer;

        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function complete(array $data = []): bool
    {
        DB::beginTransaction();

        try {
            $this->update([
                'status'       => TransferStatus::COMPLETED,
                'processed_at' => now(),
                'metadata'     => array_merge($this->metadata ?? [], $data['metadata'] ?? []),
            ]);

            $this->transactions()->update([
                'status'       => TransactionStatus::COMPLETED,
                'processed_at' => now(),
            ]);

            DB::commit();

            return true;

        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function fail(string $reason, array $data = []): bool
    {
        DB::beginTransaction();

        try {
            $this->update([
                'status'         => TransferStatus::FAILED,
                'failed_at'      => now(),
                'failure_reason' => $reason,
                'metadata'       => array_merge($this->metadata ?? [], $data['metadata'] ?? []),
            ]);

            $this->transactions()->update([
                'status'         => TransactionStatus::FAILED,
                'failed_at'      => now(),
                'failure_reason' => $reason,
            ]);

            DB::commit();

            return true;

        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function cancel(?string $reason = null): bool
    {
        DB::beginTransaction();

        try {
            $this->update([
                'status'         => TransferStatus::CANCELLED,
                'failure_reason' => $reason,
            ]);

            $this->transactions()->update([
                'status'         => TransactionStatus::CANCELLED,
                'failure_reason' => $reason,
            ]);

            DB::commit();

            return true;

        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function isPending(): bool
    {
        return $this->status === TransferStatus::PENDING;
    }

    public function isCompleted(): bool
    {
        return $this->status === TransferStatus::COMPLETED;
    }

    public function isFailed(): bool
    {
        return $this->status === TransferStatus::FAILED;
    }

    public function isCancelled(): bool
    {
        return $this->status === TransferStatus::CANCELLED;
    }

    public function getFormattedAmount(): string
    {
        return wallet_format_amount($this->amount, $this->currency);
    }

    public function getFormattedTotalAmount(): string
    {
        return wallet_format_amount($this->total_amount, $this->currency);
    }

    public function getFormattedFee(): string
    {
        return wallet_format_amount($this->fee, $this->currency);
    }

    public function getAge(): string
    {
        return $this->created_at->diffForHumans();
    }

    public function scopeWithStatus($query, TransferStatus $status)
    {
        return $query->where('status', $status);
    }

    public function scopeForCurrency($query, string $currency)
    {
        return $query->where('currency', $currency);
    }

    public function scopePending($query)
    {
        return $query->where('status', TransferStatus::PENDING);
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', TransferStatus::COMPLETED);
    }

    public function scopeFailed($query)
    {
        return $query->where('status', TransferStatus::FAILED);
    }

    public function scopeForCustomer($query, int $customerId)
    {
        return $query->where(function ($q) use ($customerId) {
            $q->where('from_customer_id', $customerId)
                ->orWhere('to_customer_id', $customerId);
        });
    }

    public function scopeOutgoingForCustomer($query, int $customerId)
    {
        return $query->where('from_customer_id', $customerId);
    }

    public function scopeIncomingForCustomer($query, int $customerId)
    {
        return $query->where('to_customer_id', $customerId);
    }
}
