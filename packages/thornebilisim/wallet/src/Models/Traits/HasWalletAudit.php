<?php

namespace Thorne\Wallet\Models\Traits;

use OwenIt\Auditing\Auditable;

trait HasWalletAudit
{
    use Auditable;

    protected array $auditExclude = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected array $auditInclude = [];

    protected bool $auditStrict = true;

    protected array $auditableEvents = [
        'created',
        'updated',
        'deleted',
        'restored',
    ];
}
