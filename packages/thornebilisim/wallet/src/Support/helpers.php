<?php

if (! function_exists('wallet_config')) {
    function wallet_config(?string $key = null, mixed $default = null): mixed
    {
        if ($key === null) {
            return config('wallet');
        }

        return config("wallet.{$key}", $default);
    }
}

if (! function_exists('wallet_currency_config')) {
    function wallet_currency_config(string $currency, ?string $key = null, mixed $default = null): mixed
    {
        $currencyConfig = wallet_config("currencies.{$currency}", []);

        if ($key === null) {
            return $currencyConfig;
        }

        return data_get($currencyConfig, $key, $default);
    }
}

if (! function_exists('wallet_payment_method_config')) {
    function wallet_payment_method_config(string $method, ?string $key = null, mixed $default = null): mixed
    {
        $methodConfig = wallet_config("payment_methods.{$method}", []);

        if ($key === null) {
            return $methodConfig;
        }

        return data_get($methodConfig, $key, $default);
    }
}

if (! function_exists('wallet_format_amount')) {
    function wallet_format_amount(float|string $amount, string $currency): string
    {
        $precision = wallet_currency_config($currency, 'precision', 2);

        return number_format((float) $amount, $precision, '.', '');
    }
}

if (! function_exists('wallet_generate_reference')) {
    function wallet_generate_reference(string $type = 'TXN'): string
    {
        $prefix = wallet_config('transactions.reference_prefix', 'WLT-');
        $uuid   = str()->uuid()->toString();

        $shortUuid = substr(str_replace('-', '', $uuid), 0, 16);

        return "{$prefix}{$type}-{$shortUuid}";
    }
}

if (! function_exists('wallet_is_supported_currency')) {
    function wallet_is_supported_currency(string $currency): bool
    {
        $currencies = wallet_config('currencies', []);

        return isset($currencies[$currency]) && $currencies[$currency]['enabled'] === true;
    }
}

if (! function_exists('wallet_get_default_currency')) {
    function wallet_get_default_currency(): ?string
    {
        $currencies = wallet_config('currencies', []);

        foreach ($currencies as $code => $config) {
            if ($config['is_default'] === true && $config['enabled'] === true) {
                return $code;
            }
        }

        return null;
    }
}

if (! function_exists('wallet_get_enabled_currencies')) {
    function wallet_get_enabled_currencies(): array
    {
        $currencies = wallet_config('currencies', []);

        return array_filter($currencies, fn ($config) => $config['enabled'] === true);
    }
}
