<?php

namespace Thorne\Wallet\Console\Commands;

use Exception;
use Illuminate\Console\Command;
use Thorne\Wallet\Models\WalletTransfer;
use Thorne\Wallet\Services\TransferService;

class ProcessPendingTransfersCommand extends Command
{
    protected $signature = 'wallet:process-transfers
                            {--limit=100 : Maximum number of transfers to process}
                            {--timeout=24 : Timeout in hours for pending transfers}';

    protected $description = 'Process pending wallet transfers';

    public function handle(TransferService $transferService): int
    {
        $limit   = (int) $this->option('limit');
        $timeout = (int) $this->option('timeout');

        $pendingTransfers = WalletTransfer::pending()
            ->where('created_at', '>=', now()->subHours($timeout))
            ->limit($limit)
            ->get();

        if ($pendingTransfers->isEmpty()) {
            $this->info('No pending transfers to process');

            return self::SUCCESS;
        }

        $this->info("Processing {$pendingTransfers->count()} pending transfers...");

        $progressBar = $this->output->createProgressBar($pendingTransfers->count());
        $progressBar->start();

        $processed = 0;
        $failed    = 0;

        foreach ($pendingTransfers as $transfer) {
            try {
                $transferService->processTransfer($transfer);
                $processed++;
            } catch (Exception $exception) {
                $failed++;
                $this->newLine();
                $this->error("Failed to process transfer {$transfer->reference}: {$exception->getMessage()}");

                try {
                    $transfer->fail("Auto-processing failed: {$exception->getMessage()}");
                } catch (Exception $failException) {
                    $this->error("Failed to mark transfer as failed: {$failException->getMessage()}");
                }
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();

        $expiredTransfers = WalletTransfer::pending()
            ->where('created_at', '<', now()->subHours($timeout))
            ->get();

        if ($expiredTransfers->isNotEmpty()) {
            $this->info("Found {$expiredTransfers->count()} expired transfers, cancelling...");

            foreach ($expiredTransfers as $transfer) {
                try {
                    $transferService->cancelTransfer($transfer, 'Transfer expired after '.$timeout.' hours');
                } catch (Exception $exception) {
                    $this->error("Failed to cancel expired transfer {$transfer->reference}: {$exception->getMessage()}");
                }
            }
        }

        $this->info("Processing completed: {$processed} successful, {$failed} failed");

        return $failed > 0 ? self::FAILURE : self::SUCCESS;
    }
}
