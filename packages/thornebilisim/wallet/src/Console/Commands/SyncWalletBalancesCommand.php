<?php

namespace Thorne\Wallet\Console\Commands;

use Exception;
use Illuminate\Console\Command;
use Thorne\Wallet\Models\WalletBalance;
use Thorne\Wallet\Services\BalanceService;

class SyncWalletBalancesCommand extends Command
{
    protected $signature = 'wallet:sync-balances
                            {--customer-id= : Sync balances for specific customer}
                            {--currency= : Sync balances for specific currency}
                            {--max-age=30 : Maximum age in minutes for last sync}
                            {--force : Force sync all balances regardless of last sync time}';

    protected $description = 'Sync wallet balances with WEB3 service';

    public function handle(BalanceService $balanceService): int
    {
        if (! wallet_config('web3.enabled', true)) {
            $this->error('WEB3 integration is disabled');

            return self::FAILURE;
        }

        $customerId = $this->option('customer-id');
        $currency   = $this->option('currency');
        $maxAge     = (int) $this->option('max-age');
        $force      = $this->option('force');

        $query = WalletBalance::query();

        if ($customerId) {
            $query->where('customer_id', $customerId);
        }

        if ($currency) {
            $query->where('currency', $currency);
        }

        if (! $force) {
            $query->needsSync($maxAge);
        }

        $balances = $query->get();

        if ($balances->isEmpty()) {
            $this->info('No balances need syncing');

            return self::SUCCESS;
        }

        $this->info("Syncing {$balances->count()} balances...");

        $progressBar = $this->output->createProgressBar($balances->count());
        $progressBar->start();

        $synced = 0;
        $failed = 0;

        foreach ($balances as $balance) {
            try {
                $balanceService->syncWithWeb3($balance->customer_id, $balance->currency);
                $synced++;
            } catch (Exception $exception) {
                $failed++;
                $this->newLine();
                $this->error("Failed to sync balance for customer {$balance->customer_id} ({$balance->currency}): {$exception->getMessage()}");
            }

            $progressBar->advance();
        }

        $progressBar->finish();
        $this->newLine();

        $this->info("Sync completed: {$synced} successful, {$failed} failed");

        return $failed > 0 ? self::FAILURE : self::SUCCESS;
    }
}
