<?php

namespace Thorne\Wallet\Auditing\Resolvers;

use Exception;
use OwenIt\Auditing\Contracts\UserResolver;
use Thorne\Wallet\Services\CustomerSessionService;
use Illuminate\Support\Facades\Log;

class CustomerIdResolver implements UserResolver
{
    public function __construct(
        protected CustomerSessionService $customerSessionService
    ) {}

    public static function resolve()
    {
        $resolver = app(static::class);

        try {
            return $resolver->customerSessionService->resolve();
        } catch (Exception $exception) {
            Log::channel(wallet_config('logging.channel', 'wallet'))
                ->error('Failed to resolve customer', [
                    'error' => $exception->getMessage(),
                ]);

            return null;
        }
    }
}
