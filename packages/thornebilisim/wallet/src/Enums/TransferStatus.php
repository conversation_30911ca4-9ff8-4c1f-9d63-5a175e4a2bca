<?php

namespace Thorne\Wallet\Enums;

enum TransferStatus: string
{
    case PENDING    = 'pending';
    case PROCESSING = 'processing';
    case COMPLETED  = 'completed';
    case FAILED     = 'failed';
    case CANCELLED  = 'cancelled';

    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function label(): string
    {
        return match ($this) {
            self::PENDING    => 'Pending',
            self::PROCESSING => 'Processing',
            self::COMPLETED  => 'Completed',
            self::FAILED     => 'Failed',
            self::CANCELLED  => 'Cancelled',
        };
    }

    public function description(): string
    {
        return match ($this) {
            self::PENDING    => 'Transfer is waiting to be processed',
            self::PROCESSING => 'Transfer is being processed',
            self::COMPLETED  => 'Transfer has been completed successfully',
            self::FAILED     => 'Transfer has failed',
            self::CANCELLED  => 'Transfer has been cancelled',
        };
    }

    public function isFinal(): bool
    {
        return in_array($this, [self::COMPLETED, self::FAILED, self::CANCELLED]);
    }

    public function isActive(): bool
    {
        return in_array($this, [self::PENDING, self::PROCESSING]);
    }

    public function isSuccessful(): bool
    {
        return $this === self::COMPLETED;
    }

    public function isFailure(): bool
    {
        return in_array($this, [self::FAILED, self::CANCELLED]);
    }
}
