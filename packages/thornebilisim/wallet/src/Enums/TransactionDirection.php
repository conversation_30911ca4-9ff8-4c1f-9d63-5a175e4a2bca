<?php

namespace Thorne\Wallet\Enums;

enum TransactionDirection: string
{
    case INBOUND  = 'inbound';
    case OUTBOUND = 'outbound';

    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function label(): string
    {
        return match ($this) {
            self::INBOUND  => 'Inbound',
            self::OUTBOUND => 'Outbound',
        };
    }

    public function description(): string
    {
        return match ($this) {
            self::INBOUND  => 'Money coming into the account',
            self::OUTBOUND => 'Money going out of the account',
        };
    }

    public function opposite(): self
    {
        return match ($this) {
            self::INBOUND  => self::OUTBOUND,
            self::OUTBOUND => self::INBOUND,
        };
    }

    public function isInbound(): bool
    {
        return $this === self::INBOUND;
    }

    public function isOutbound(): bool
    {
        return $this === self::OUTBOUND;
    }
}
