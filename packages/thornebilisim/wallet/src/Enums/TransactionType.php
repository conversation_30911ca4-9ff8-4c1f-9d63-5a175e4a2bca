<?php

namespace Thorne\Wallet\Enums;

enum TransactionType: string
{
    case DEPOSIT    = 'deposit';
    case WITHDRAWAL = 'withdrawal';
    case TRANSFER   = 'transfer';
    case FEE        = 'fee';
    case REFUND     = 'refund';
    case ADJUSTMENT = 'adjustment';

    public static function all(): array
    {
        return array_column(self::cases(), 'value');
    }

    public function label(): string
    {
        return match ($this) {
            self::DEPOSIT    => 'Deposit',
            self::WITHDRAWAL => 'Withdrawal',
            self::TRANSFER   => 'Transfer',
            self::FEE        => 'Fee',
            self::REFUND     => 'Refund',
            self::ADJUSTMENT => 'Adjustment',
        };
    }

    public function description(): string
    {
        return match ($this) {
            self::DEPOSIT    => 'Money added to wallet',
            self::WITHDRAWAL => 'Money withdrawn from wallet',
            self::TRANSFER   => 'Money transferred between accounts',
            self::FEE        => 'Transaction fee',
            self::REFUND     => 'Money refunded to wallet',
            self::ADJUSTMENT => 'Balance adjustment',
        };
    }

    public function isPositive(): bool
    {
        return in_array($this, [self::DEPOSIT, self::REFUND, self::ADJUSTMENT]);
    }

    public function isNegative(): bool
    {
        return in_array($this, [self::WITHDRAWAL, self::FEE]);
    }

    public function isTransfer(): bool
    {
        return $this === self::TRANSFER;
    }
}
