<?php

namespace Thorne\Wallet\Exceptions;

class WalletOperationException extends WalletException
{
    public static function missingRequiredField(string $field): self
    {
        return new self(
            "Missing required field: {$field}",
            'MISSING_REQUIRED_FIELD',
            ['field' => $field]
        );
    }

    public static function invalidCurrency(string $currency): self
    {
        return new self(
            "Unsupported currency: {$currency}",
            'INVALID_CURRENCY',
            ['currency' => $currency]
        );
    }

    public static function invalidAmount(float $amount, string $reason = 'Amount must be greater than zero'): self
    {
        return new self(
            $reason,
            'INVALID_AMOUNT',
            ['amount' => $amount]
        );
    }

    public static function invalidPaymentMethod(string $method, string $operation): self
    {
        return new self(
            "Invalid or disabled {$operation} method: {$method}",
            'INVALID_PAYMENT_METHOD',
            [
                'payment_method' => $method,
                'operation' => $operation
            ]
        );
    }

    public static function invalidAccountNumber(string $accountNumber, string $reason = 'Invalid format'): self
    {
        return new self(
            "Invalid account number '{$accountNumber}': {$reason}",
            'INVALID_ACCOUNT_NUMBER',
            [
                'account_number' => $accountNumber,
                'reason' => $reason
            ]
        );
    }

    public static function currencyMismatch(string $expected, string $actual): self
    {
        return new self(
            "Currency mismatch. Expected: {$expected}, Got: {$actual}",
            'CURRENCY_MISMATCH',
            [
                'expected_currency' => $expected,
                'actual_currency' => $actual
            ]
        );
    }

    public static function sameAccountOperation(string $accountNumber): self
    {
        return new self(
            "Cannot perform operation on the same account: {$accountNumber}",
            'SAME_ACCOUNT_OPERATION',
            ['account_number' => $accountNumber]
        );
    }

    public static function accountGenerationFailed(string $currency, int $attempts): self
    {
        return new self(
            "Failed to generate unique account number for currency {$currency} after {$attempts} attempts",
            'ACCOUNT_GENERATION_FAILED',
            [
                'currency' => $currency,
                'attempts' => $attempts,
                'suggestion' => 'Please contact system administrator',
            ]
        );
    }

    public static function accountNotFound(string $accountNumber): self
    {
        return new self(
            "Account not found: {$accountNumber}",
            'ACCOUNT_NOT_FOUND',
            ['account_number' => $accountNumber]
        );
    }

    public static function accountNotOwned(string $accountNumber, int $customerId): self
    {
        return new self(
            "Account {$accountNumber} is not owned by customer {$customerId}",
            'ACCOUNT_NOT_OWNED',
            [
                'account_number' => $accountNumber,
                'customer_id' => $customerId,
            ]
        );
    }

    public static function accountInactive(string $accountNumber): self
    {
        return new self(
            "Account is inactive: {$accountNumber}",
            'ACCOUNT_INACTIVE',
            ['account_number' => $accountNumber]
        );
    }

    public static function insufficientBalance(float $required, float $available, string $currency): self
    {
        return new self(
            "Insufficient balance. Required: {$required} {$currency}, Available: {$available} {$currency}",
            'INSUFFICIENT_BALANCE',
            [
                'required_amount' => $required,
                'available_amount' => $available,
                'currency' => $currency,
                'deficit' => $required - $available
            ]
        );
    }

    public static function balanceLockFailed(float $amount, string $currency): self
    {
        return new self(
            "Failed to lock balance amount: {$amount} {$currency}",
            'BALANCE_LOCK_FAILED',
            [
                'amount' => $amount,
                'currency' => $currency,
            ]
        );
    }

    public static function balanceUnlockFailed(float $amount, string $currency): self
    {
        return new self(
            "Failed to unlock balance amount: {$amount} {$currency}",
            'BALANCE_UNLOCK_FAILED',
            [
                'amount' => $amount,
                'currency' => $currency,
            ]
        );
    }

    public static function balanceNotFound(int $customerId, string $currency): self
    {
        return new self(
            "Balance not found for customer {$customerId} in currency {$currency}",
            'BALANCE_NOT_FOUND',
            [
                'customer_id' => $customerId,
                'currency' => $currency,
            ]
        );
    }

    public static function lockAmountInvalid(float $amount): self
    {
        return new self(
            'Lock amount must be greater than zero',
            'INVALID_LOCK_AMOUNT',
            ['amount' => $amount]
        );
    }

    public static function unlockAmountInvalid(float $amount): self
    {
        return new self(
            'Unlock amount must be greater than zero',
            'INVALID_UNLOCK_AMOUNT',
            ['amount' => $amount]
        );
    }

    public static function insufficientLockedBalance(float $requested, float $available): self
    {
        return new self(
            'Insufficient locked balance to unlock',
            'INSUFFICIENT_LOCKED_BALANCE',
            [
                'requested_amount' => $requested,
                'available_locked' => $available
            ]
        );
    }

    public static function insufficientAvailableBalance(float $requested, float $available): self
    {
        return new self(
            'Insufficient available balance to lock',
            'INSUFFICIENT_AVAILABLE_BALANCE',
            [
                'requested_amount' => $requested,
                'available_balance' => $available
            ]
        );
    }

    public static function transferLimitExceeded(string $limitType, float $limit, float $usage, string $currency): self
    {
        $remaining = max(0, $limit - $usage);

        return new self(
            "{$limitType} transfer limit exceeded. Limit: {$limit} {$currency}, Usage: {$usage} {$currency}, Remaining: {$remaining} {$currency}",
            'TRANSFER_LIMIT_EXCEEDED',
            [
                'limit_type' => $limitType,
                'limit' => $limit,
                'usage' => $usage,
                'remaining' => $remaining,
                'currency' => $currency,
            ]
        );
    }

    public static function transfersDisabled(): self
    {
        return new self(
            'Transfers are currently disabled',
            'TRANSFERS_DISABLED'
        );
    }

    public static function amountBelowMinimum(float $amount, float $minimum, string $currency): self
    {
        return new self(
            "Amount {$amount} {$currency} is below minimum of {$minimum} {$currency}",
            'AMOUNT_BELOW_MINIMUM',
            [
                'amount' => $amount,
                'minimum' => $minimum,
                'currency' => $currency
            ]
        );
    }

    public static function amountAboveMaximum(float $amount, float $maximum, string $currency): self
    {
        return new self(
            "Amount {$amount} {$currency} exceeds maximum of {$maximum} {$currency}",
            'AMOUNT_ABOVE_MAXIMUM',
            [
                'amount' => $amount,
                'maximum' => $maximum,
                'currency' => $currency
            ]
        );
    }

    public static function transactionNotDeposit(string $transactionId): self
    {
        return new self(
            "Transaction {$transactionId} is not a deposit",
            'TRANSACTION_NOT_DEPOSIT',
            ['transaction_id' => $transactionId]
        );
    }

    public static function transactionNotWithdrawal(string $transactionId): self
    {
        return new self(
            "Transaction {$transactionId} is not a withdrawal",
            'TRANSACTION_NOT_WITHDRAWAL',
            ['transaction_id' => $transactionId]
        );
    }

    public static function transferNotPending(string $transferId, string $currentStatus): self
    {
        return new self(
            "Transfer {$transferId} is not in pending status. Current status: {$currentStatus}",
            'TRANSFER_NOT_PENDING',
            [
                'transfer_id' => $transferId,
                'current_status' => $currentStatus
            ]
        );
    }

    public static function operationNotAllowed(string $operation, int $customerId, float $amount, string $currency, array $errors): self
    {
        return new self(
            'Operation not allowed: ' . implode(', ', $errors),
            'OPERATION_NOT_ALLOWED',
            [
                'operation' => $operation,
                'customer_id' => $customerId,
                'amount' => $amount,
                'currency' => $currency,
                'errors' => $errors,
            ]
        );
    }

    public static function customerNotFound(int $customerId): self
    {
        return new self(
            "Customer not found: {$customerId}",
            'CUSTOMER_NOT_FOUND',
            ['customer_id' => $customerId]
        );
    }

    public function getOperation(): ?string
    {
        return $this->context['operation'] ?? null;
    }

    public function getCustomerId(): ?int
    {
        return $this->context['customer_id'] ?? null;
    }

    public function getAmount(): ?float
    {
        return $this->context['amount'] ?? null;
    }

    public function getCurrency(): ?string
    {
        return $this->context['currency'] ?? null;
    }

    public function getErrors(): array
    {
        return $this->context['errors'] ?? [];
    }
}
