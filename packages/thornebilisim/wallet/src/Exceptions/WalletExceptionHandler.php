<?php

namespace Thorne\Wallet\Exceptions;

use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

class WalletExceptionHandler
{
    public static function handle(Throwable $exception, Request $request): ?JsonResponse
    {
        if (! self::isWalletApiRequest($request)) {
            return null;
        }

        self::logException($exception, $request);

        return match (true) {
            $exception instanceof ModelNotFoundException    => self::handleModelNotFound($exception, $request),
            $exception instanceof NotFoundHttpException     => self::handleNotFound($exception, $request),
            $exception instanceof ValidationException       => self::handleValidation($exception, $request),
            $exception instanceof WalletPermissionException => self::handleWalletPermission($exception, $request),
            $exception instanceof WalletException           => self::handleWalletException($exception, $request),
            $exception instanceof HttpException             => self::handleHttpException($exception, $request),
            default                                         => self::handleGenericException($exception, $request),
        };
    }

    protected static function isWalletApiRequest(Request $request): bool
    {
        $path = $request->path();

        return str_starts_with($path, 'api/wallet') ||
               str_contains($request->route()?->getName() ?? '', 'wallet.');
    }

    protected static function handleModelNotFound(ModelNotFoundException $exception, Request $request): JsonResponse
    {
        $model = class_basename($exception->getModel());
        $ids   = $exception->getIds();

        $message = match ($model) {
            'WalletTransaction' => 'Transaction not found',
            'WalletBalance'     => 'Balance record not found',
            'WalletAccount'     => 'Wallet account not found',
            default             => "{$model} not found",
        };

        return response()->json([
            'success' => false,
            'error'   => 'RESOURCE_NOT_FOUND',
            'message' => $message,
            'data'    => [
                'resource_type' => strtolower(str_replace('Wallet', '', $model)),
                'resource_id'   => is_array($ids) ? $ids[0] ?? null : $ids,
            ],
            'meta' => [
                'timestamp'  => now()->toISOString(),
                'request_id' => $request->header('X-Request-ID', uniqid()),
                'endpoint'   => $request->path(),
            ],
        ], 404);
    }

    protected static function handleNotFound(NotFoundHttpException $exception, Request $request): JsonResponse
    {
        return response()->json([
            'success' => false,
            'error'   => 'ENDPOINT_NOT_FOUND',
            'message' => 'The requested endpoint was not found',
            'data'    => [
                'endpoint'            => $request->path(),
                'method'              => $request->method(),
                'available_endpoints' => self::getAvailableEndpoints(),
            ],
            'meta' => [
                'timestamp'  => now()->toISOString(),
                'request_id' => $request->header('X-Request-ID', uniqid()),
            ],
        ], 404);
    }

    protected static function handleValidation(ValidationException $exception, Request $request): JsonResponse
    {
        return response()->json([
            'success' => false,
            'error'   => 'VALIDATION_ERROR',
            'message' => 'The given data was invalid',
            'data'    => [
                'errors'       => $exception->errors(),
                'failed_rules' => self::getFailedRules($exception),
            ],
            'meta' => [
                'timestamp'  => now()->toISOString(),
                'request_id' => $request->header('X-Request-ID', uniqid()),
                'endpoint'   => $request->path(),
            ],
        ], 422);
    }

    protected static function handleWalletPermission(WalletPermissionException $exception, Request $request): JsonResponse
    {
        return response()->json([
            'success' => false,
            'error'   => 'PERMISSION_DENIED',
            'message' => $exception->getMessage(),
            'data'    => [
                'operation'   => $exception->getOperation(),
                'customer_id' => $exception->getCustomerId(),
                'amount'      => $exception->getAmount(),
                'currency'    => $exception->getCurrency(),
                'errors'      => $exception->getErrors(),
                'context'     => $exception->getContext(),
            ],
            'meta' => [
                'timestamp'  => now()->toISOString(),
                'request_id' => $request->header('X-Request-ID', uniqid()),
                'endpoint'   => $request->path(),
            ],
        ], 403);
    }

    protected static function handleWalletException(WalletException $exception, Request $request): JsonResponse
    {
        return response()->json([
            'success' => false,
            'error'   => 'WALLET_ERROR',
            'message' => $exception->getMessage(),
            'data'    => [
                'error_code' => $exception->getCode(),
                'context'    => method_exists($exception, 'getContext') ? $exception->getContext() : [],
            ],
            'meta' => [
                'timestamp'  => now()->toISOString(),
                'request_id' => $request->header('X-Request-ID', uniqid()),
                'endpoint'   => $request->path(),
            ],
        ], $exception->getCode() ?: 400);
    }

    protected static function handleHttpException(HttpException $exception, Request $request): JsonResponse
    {
        $statusCode = $exception->getStatusCode();

        $message = match ($statusCode) {
            400     => 'Bad Request',
            401     => 'Unauthorized',
            403     => 'Forbidden',
            404     => 'Not Found',
            405     => 'Method Not Allowed',
            422     => 'Unprocessable Entity',
            429     => 'Too Many Requests',
            500     => 'Internal Server Error',
            503     => 'Service Unavailable',
            default => 'HTTP Error',
        };

        return response()->json([
            'success' => false,
            'error'   => 'HTTP_ERROR',
            'message' => $exception->getMessage() ?: $message,
            'data'    => [
                'status_code' => $statusCode,
                'headers'     => $exception->getHeaders(),
            ],
            'meta' => [
                'timestamp'  => now()->toISOString(),
                'request_id' => $request->header('X-Request-ID', uniqid()),
                'endpoint'   => $request->path(),
            ],
        ], $statusCode);
    }

    protected static function handleGenericException(Throwable $exception, Request $request): JsonResponse
    {
        $isDevelopment  = config('app.debug', false);
        $includeTrace   = wallet_config('exceptions.include_trace_in_response', false);
        $includeContext = wallet_config('exceptions.include_context_in_response', true);
        $maxTraceLines  = wallet_config('exceptions.max_trace_lines', 5);
        $errorIdPrefix  = wallet_config('exceptions.error_id_prefix', 'WLT_ERR_');

        $data = [];

        if ($isDevelopment || $includeTrace) {
            $data['exception'] = get_class($exception);
            $data['file']      = $exception->getFile();
            $data['line']      = $exception->getLine();

            if ($includeTrace) {
                $data['trace'] = collect($exception->getTrace())->take($maxTraceLines)->toArray();
            }
        }

        if (! $isDevelopment && ! $includeTrace) {
            $data['error_id'] = uniqid($errorIdPrefix);
        }

        return response()->json([
            'success' => false,
            'error'   => 'INTERNAL_ERROR',
            'message' => ($isDevelopment || $includeContext) ? $exception->getMessage() : 'An unexpected error occurred',
            'data'    => $data,
            'meta'    => [
                'timestamp'  => now()->toISOString(),
                'request_id' => $request->header('X-Request-ID', uniqid()),
                'endpoint'   => $request->path(),
            ],
        ], 500);
    }

    protected static function logException(Throwable $exception, Request $request): void
    {
        if (! wallet_config('logging.enabled', true)) {
            return;
        }

        $context = [
            'exception' => get_class($exception),
            'message'   => $exception->getMessage(),
            'file'      => $exception->getFile(),
            'line'      => $exception->getLine(),
            'request'   => [
                'method'      => $request->method(),
                'url'         => $request->fullUrl(),
                'ip'          => $request->ip(),
                'user_agent'  => $request->userAgent(),
                'customer_id' => $request->header('X-Customer-Id'),
            ],
        ];

        Log::channel(wallet_config('logging.channel', 'wallet'))
            ->error('Wallet API Exception', $context);
    }

    protected static function getAvailableActions(string $model): array
    {
        return match ($model) {
            'WalletTransaction' => [
                'list_transactions' => 'GET /api/wallet/transactions',
                'create_deposit'    => 'POST /api/wallet/deposits',
                'create_withdrawal' => 'POST /api/wallet/withdrawals',
                'create_transfer'   => 'POST /api/wallet/transfers',
            ],
            'WalletBalance' => [
                'get_balances' => 'GET /api/wallet/balances',
                'sync_balance' => 'POST /api/wallet/balances/sync',
            ],
            'WalletAccount' => [
                'get_accounts'   => 'GET /api/wallet/accounts',
                'create_account' => 'POST /api/wallet/accounts',
            ],
            default => [
                'wallet_overview' => 'GET /api/wallet',
            ],
        };
    }

    protected static function getAvailableEndpoints(): array
    {
        return [
            'wallet_overview' => 'GET /api/wallet',
            'balances'        => 'GET /api/wallet/balances',
            'transactions'    => 'GET /api/wallet/transactions',
            'deposits'        => 'POST /api/wallet/deposits',
            'withdrawals'     => 'POST /api/wallet/withdrawals',
            'transfers'       => 'POST /api/wallet/transfers',
            'permissions'     => 'GET /api/wallet/permissions/summary',
        ];
    }

    protected static function getFailedRules(ValidationException $exception): array
    {
        $failedRules = [];

        if (method_exists($exception->validator, 'failed')) {
            foreach ($exception->validator->failed() as $field => $rules) {
                $failedRules[$field] = array_keys($rules);
            }
        }

        return $failedRules;
    }

    public static function shouldHandle(Throwable $exception, Request $request): bool
    {
        if (self::isWalletApiRequest($request)) {
            return true;
        }

        if ($exception instanceof WalletException || $exception instanceof WalletPermissionException) {
            return true;
        }

        if ($exception instanceof ModelNotFoundException) {
            $model = $exception->getModel();

            return str_contains($model, 'Thorne\\Wallet\\');
        }

        return false;
    }
}
