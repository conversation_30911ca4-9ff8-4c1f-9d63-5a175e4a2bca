<?php

namespace Thorne\Wallet\Exceptions;

class WalletAccountException extends WalletException
{
    public static function generationFailed(string $currency, int $attempts): self
    {
        return new self(
            "Failed to generate unique account number for currency {$currency} after {$attempts} attempts",
            'ACCOUNT_GENERATION_FAILED',
            [
                'currency'   => $currency,
                'attempts'   => $attempts,
                'suggestion' => 'Please contact system administrator',
            ]
        );
    }

    public static function notFound(string $accountNumber): self
    {
        return new self(
            "Account not found: {$accountNumber}",
            'ACCOUNT_NOT_FOUND',
            [
                'account_number' => $accountNumber,
            ]
        );
    }

    public static function notOwned(string $accountNumber, int $customerId): self
    {
        return new self(
            "Account {$accountNumber} is not owned by customer {$customerId}",
            'ACCOUNT_NOT_OWNED',
            [
                'account_number' => $accountNumber,
                'customer_id'    => $customerId,
            ]
        );
    }

    public static function inactive(string $accountNumber): self
    {
        return new self(
            "Account is inactive: {$accountNumber}",
            'ACCOUNT_INACTIVE',
            [
                'account_number' => $accountNumber,
            ]
        );
    }
}
