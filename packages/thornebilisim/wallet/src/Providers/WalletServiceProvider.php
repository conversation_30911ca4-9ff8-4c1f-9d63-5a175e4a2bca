<?php

namespace Thorne\Wallet\Providers;

use Exception;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;
use OwenIt\Auditing\AuditingServiceProvider;
use Thorne\Wallet\Http\Middleware\CustomerAuthMiddleware;
use Thorne\Wallet\Http\Middleware\WalletExceptionMiddleware;
use Thorne\Wallet\Models\WalletAccount;
use Thorne\Wallet\Models\WalletBalance;
use Thorne\Wallet\Models\WalletTransaction;
use Thorne\Wallet\Models\WalletTransfer;
use Thorne\Wallet\Services\BalanceService;
use Thorne\Wallet\Services\CompanyAccountService;
use Thorne\Wallet\Services\ConfigValidationService;
use Thorne\Wallet\Services\CustomerSessionService;
use Thorne\Wallet\Services\LuhnService;
use Thorne\Wallet\Services\PermissionService;
use Thorne\Wallet\Services\TransferService;
use Thorne\Wallet\Services\WalletService;
use Thorne\Wallet\Services\Web3ApiClient;
use Webkul\Customer\Models\Customer;
use Illuminate\Contracts\Debug\ExceptionHandler;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;

class WalletServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->registerConfig();
        $this->registerServices();
        $this->registerFacades();
        $this->registerAuditing();
    }

    public function boot(Router $router): void
    {
        if (! wallet_config('enabled', true)) {
            return;
        }

        $this->bootPackageResources();
        $this->bootMiddleware($router);
        $this->bootCustomerRelations();
        $this->bootConfigValidation();

        $router->aliasMiddleware('wallet.customer', CustomerAuthMiddleware::class);

        $this->app->make(ExceptionHandler::class)
            ->renderable(function (ModelNotFoundException $e, $request): ?JsonResponse {
                if ($request->wantsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Kayıt bulunamadı',
                    ], 404);
                }

                return null;
            });
    }

    protected function registerConfig(): void
    {
        $packageRoot = dirname(__DIR__, 2);
        $this->mergeConfigFrom($packageRoot.'/config/wallet.php', 'wallet');
        $this->mergeConfigFrom($packageRoot.'/config/logging.php', 'logging.channels');
    }

    protected function registerServices(): void
    {
        $this->app->singleton(LuhnService::class, function () {
            return new LuhnService;
        });

        $this->app->singleton(CustomerSessionService::class, function () {
            return new CustomerSessionService;
        });

        $this->app->singleton(WalletService::class, function ($app) {
            return new WalletService(
                $app->make(LuhnService::class),
                $app->make(BalanceService::class),
                $app->make(TransferService::class),
                $app->make(PermissionService::class)
            );
        });

        $this->app->singleton(BalanceService::class, function () {
            return new BalanceService;
        });

        $this->app->singleton(TransferService::class, function ($app) {
            return new TransferService(
                $app->make(BalanceService::class)
            );
        });

        $this->app->singleton(Web3ApiClient::class, function () {
            return new Web3ApiClient;
        });

        $this->app->singleton(CompanyAccountService::class, function () {
            return new CompanyAccountService;
        });

        $this->app->singleton(PermissionService::class, function () {
            return new PermissionService;
        });

        $this->app->singleton(ConfigValidationService::class, function () {
            return new ConfigValidationService;
        });
    }

    protected function registerFacades(): void
    {
        $this->app->alias(LuhnService::class, 'wallet.luhn');
        $this->app->alias(WalletService::class, 'wallet.service');
        $this->app->alias(Web3ApiClient::class, 'wallet.web3');
    }

    protected function bootPackageResources(): void
    {
        $packageRoot = dirname(__DIR__, 2);

        $this->loadMigrationsFrom($packageRoot.'/database/migrations');

        $this->loadRoutesFrom($packageRoot.'/routes/web.php');
        $this->loadRoutesFrom($packageRoot.'/routes/api.php');

        $this->loadViewsFrom($packageRoot.'/resources/views', 'wallet');

        $this->loadTranslationsFrom($packageRoot.'/resources/lang', 'wallet');

        if ($this->app->runningInConsole()) {
            $this->publishes([
                $packageRoot.'/config/wallet.php' => config_path('wallet.php'),
            ], 'wallet-config');

            $this->publishes([
                $packageRoot.'/resources/views' => resource_path('views/vendor/wallet'),
            ], 'wallet-views');

            $this->publishes([
                $packageRoot.'/resources/lang' => resource_path('lang/vendor/wallet'),
            ], 'wallet-lang');

            $this->publishes([
                $packageRoot.'/database/migrations' => database_path('migrations'),
            ], 'wallet-migrations');

            $this->commands([
                //
            ]);
        }
    }

    protected function bootMiddleware(Router $router): void
    {
        $router->aliasMiddleware('wallet.exceptions', WalletExceptionMiddleware::class);

        $router->pushMiddlewareToGroup('api', WalletExceptionMiddleware::class);
    }

    protected function bootCustomerRelations(): void
    {
        if (class_exists(Customer::class)) {
            Customer::resolveRelationUsing('walletAccounts', function ($customerModel) {
                return $customerModel->hasMany(WalletAccount::class, 'customer_id');
            });

            Customer::resolveRelationUsing('walletBalances', function ($customerModel) {
                return $customerModel->hasMany(WalletBalance::class, 'customer_id');
            });

            Customer::resolveRelationUsing('walletTransactions', function ($customerModel) {
                return $customerModel->hasMany(WalletTransaction::class, 'customer_id');
            });

            Customer::resolveRelationUsing('walletTransfersFrom', function ($customerModel) {
                return $customerModel->hasMany(WalletTransfer::class, 'from_customer_id');
            });

            Customer::resolveRelationUsing('walletTransfersTo', function ($customerModel) {
                return $customerModel->hasMany(WalletTransfer::class, 'to_customer_id');
            });
        }
    }

    public function provides(): array
    {
        return [
            LuhnService::class,
            WalletService::class,
            BalanceService::class,
            TransferService::class,
            Web3ApiClient::class,
            CompanyAccountService::class,
            PermissionService::class,
            CustomerSessionService::class,
            ConfigValidationService::class,
            'wallet.luhn',
            'wallet.service',
            'wallet.web3',
        ];
    }

    protected function bootConfigValidation(): void
    {
        if (app()->environment(['local', 'testing', 'staging'])) {
            try {
                $this->app->make(ConfigValidationService::class)->validateAndLog();
            } catch (Exception $exception) {
                Log::warning('Wallet config validation failed during boot', [
                    'error' => $exception->getMessage(),
                ]);
            }
        }
    }

    protected function registerAuditing(): void
    {
        $packageRoot = dirname(__DIR__, 2);
        $this->app->register(AuditingServiceProvider::class);

        $this->mergeConfigFrom($packageRoot.'/config/audit.php', 'audit');

        if ($this->app->runningInConsole()) {
            $this->publishes([
                $packageRoot.'/config/audit.php' => config_path('audit.php'),
            ]);
        }
    }
}
