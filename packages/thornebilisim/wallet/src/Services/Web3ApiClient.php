<?php

namespace Thorne\Wallet\Services;

use Exception;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

use Thorne\Wallet\Exceptions\WalletSystemException;

class Web3ApiClient
{
    protected string $baseUrl;

    protected string $method = 'GET';

    protected ?int $timeout = null;

    protected int $retryAttempts;

    protected array $headers = [];

    protected array $queryParams = [];

    protected bool $enableLogging;

    protected string $logChannel;

    public function __construct()
    {
        $this->baseUrl       = wallet_config('web3.base_url');
        $this->timeout       = wallet_config('web3.timeout', 30);
        $this->retryAttempts = wallet_config('web3.retry_attempts', 3);
        $this->enableLogging = wallet_config('logging.enabled', true);
        $this->logChannel    = wallet_config('logging.channel', 'wallet');

        $this->headers = [
            'Content-Type' => 'application/json',
            'Accept'       => 'application/json',
        ];
    }

    public function endpoint(string $path): self
    {
        $this->baseUrl = rtrim(wallet_config('web3.base_url'), '/').'/'.ltrim($path, '/');

        return $this;
    }

    public function method(string $method): self
    {
        $this->method = strtoupper($method);

        return $this;
    }

    public function timeout(int $timeout): self
    {
        $this->timeout = $timeout;

        return $this;
    }

    public function retries(int $attempts): self
    {
        $this->retryAttempts = $attempts;

        return $this;
    }

    public function withHeaders(array $headers): self
    {
        $this->headers = array_merge($this->headers, $headers);

        return $this;
    }

    public function withQuery(array $params): self
    {
        $this->queryParams = array_merge($this->queryParams, $params);

        return $this;
    }

    public function withoutLogging(): self
    {
        $this->enableLogging = false;

        return $this;
    }

    public function get(array $query = []): Response
    {
        return $this->method('GET')->withQuery($query)->send();
    }

    public function post(array $data = []): Response
    {
        return $this->method('POST')->send($data);
    }

    public function send(array $data = []): Response
    {
        $requestId = uniqid('web3_', true);
        $startTime = microtime(true);

        try {

            $this->headers['Authorization'] = $this->generateOAuthSignature($data);

            $fullUrl = $this->baseUrl;
            if (! empty($this->queryParams)) {
                $fullUrl .= '?'.http_build_query($this->queryParams);
            }

            $response = $this->executeRequest($fullUrl, $data);
            $duration = microtime(true) - $startTime;

            $this->logRequestAndResponse($requestId, $fullUrl, $data, $response, $duration);

            return $response;

        } catch (Exception $exception) {
            $this->logError($requestId, $exception, microtime(true) - $startTime);
            throw WalletSystemException::web3ApiError(
                "Web3 API request failed: {$exception->getMessage()}",
                [
                    'original_exception' => get_class($exception),
                    'original_message' => $exception->getMessage(),
                    'original_code' => $exception->getCode()
                ]
            );
        }
    }

    protected function executeRequest(string $url, array $data = []): Response
    {
        $lastException = null;

        for ($attempt = 1; $attempt <= $this->retryAttempts; $attempt++) {
            try {
                $httpClient = Http::withHeaders($this->headers);

                if ($this->timeout) {
                    $httpClient = $httpClient->timeout($this->timeout);
                }

                $response = match ($this->method) {
                    'GET'    => $httpClient->get($url),
                    'POST'   => $httpClient->post($url, $data),
                    default  => throw WalletSystemException::unsupportedHttpMethod($this->method)
                };

                if ($response->successful()) {
                    return $response;
                }

                throw new \RuntimeException(
                    "HTTP {$response->status()}: {$response->body()}",
                    $response->status()
                );

            } catch (Exception $exception) {
                $lastException = $exception;

                if ($attempt < $this->retryAttempts) {
                    $delay = pow(2, $attempt - 1);
                    sleep($delay);

                    if ($this->enableLogging) {
                        Log::channel($this->logChannel)->warning('Web3 API request retry', [
                            'attempt'       => $attempt,
                            'max_attempts'  => $this->retryAttempts,
                            'delay_seconds' => $delay,
                            'error'         => $exception->getMessage(),
                            'url'           => $url,
                        ]);
                    }
                }
            }
        }

        throw $lastException;
    }

    protected function generateOAuthSignature(array $bodyData = []): string
    {
        $endpointUrl   = explode('?', $this->baseUrl)[0];
        $endpointQuery = parse_url($this->baseUrl);
        $jsonData      = ! empty($bodyData) ? json_encode($bodyData) : '';

        $hash       = hash('sha256', $jsonData, true);
        $base64Data = base64_encode($hash);

        $oauthParams = [
            'oauth_consumer_key'     => config('app.oauth_consumer_key'),
            'oauth_signature_method' => 'RSA-SHA256',
            'oauth_timestamp'        => (string) time(),
            'oauth_nonce'            => $this->generateNonce(11),
            'oauth_version'          => '1.0',
            'oauth_body_hash'        => $base64Data,
        ];

        $paramArray = [];
        foreach ($oauthParams as $key => $value) {
            $paramArray[] = urlencode($key).'='.urlencode($value);
        }

        if (isset($endpointQuery['query'])) {
            parse_str($endpointQuery['query'], $queryParams);
            foreach ($queryParams as $key => $value) {
                $paramArray[] = urlencode($key).'='.urlencode($value);
            }
        }

        sort($paramArray);
        $paramString = implode('&', $paramArray);

        $baseString = sprintf(
            '%s&%s&%s',
            strtoupper($this->method),
            urlencode($endpointUrl),
            urlencode($paramString)
        );

        $signature = base64_encode($this->signWithPrivateKey($baseString));

        return $this->buildAuthorizationHeader($oauthParams, $signature);
    }

    protected function signWithPrivateKey(string $data): string
    {
        $privateKeyPath = base_path('private-key.pem');

        if (! file_exists($privateKeyPath)) {
            throw new \RuntimeException('Private key file not found: '.$privateKeyPath);
        }

        $privateKey = openssl_pkey_get_private(file_get_contents($privateKeyPath));

        if (! $privateKey) {
            throw new \RuntimeException('Failed to load private key');
        }

        if (! openssl_sign($data, $signature, $privateKey, OPENSSL_ALGO_SHA256)) {
            throw new \RuntimeException('Failed to sign data with private key');
        }

        return $signature;
    }

    protected function generateNonce(int $length): string
    {
        $characters = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $nonce      = '';
        $max        = strlen($characters) - 1;

        for ($i = 0; $i < $length; $i++) {
            $nonce .= $characters[random_int(0, $max)];
        }

        return $nonce;
    }

    protected function buildAuthorizationHeader(array $oauthParams, string $signature): string
    {
        $oauthParams['oauth_signature'] = $signature;

        $headerParts = ['OAuth '];
        $first       = true;

        foreach ($oauthParams as $key => $value) {
            if (! $first) {
                $headerParts[] = ', ';
            }
            $headerParts[] = $key.'="'.$value.'"';
            $first         = false;
        }

        return implode('', $headerParts);
    }

    protected function logRequestAndResponse(string $requestId, string $url, array $data, ?Response $response = null, ?float $duration = null): void
    {
        if (! $this->enableLogging) {
            return;
        }

        $logData = [
            'request_id'     => $requestId,
            'method'         => $this->method,
            'url'            => $url,
            'headers'        => $this->sanitizeHeaders($this->headers),
            'request_data'   => $data,
            'timeout'        => $this->timeout,
            'retry_attempts' => $this->retryAttempts,
        ];

        if ($response) {
            $logData = array_merge($logData, [
                'status'           => $response->status(),
                'response_headers' => $response->headers(),
                'response_body'    => $response->json(),
                'duration_ms'      => round($duration * 1000, 2),
                'successful'       => $response->successful(),
            ]);
        }

        Log::channel($this->logChannel)->info('Web3 API Request/Response', $logData);
    }

    protected function logError(string $requestId, Exception $exception, float $duration): void
    {
        if (! $this->enableLogging) {
            return;
        }

        Log::channel($this->logChannel)->error('Web3 API Error', [
            'request_id'  => $requestId,
            'error'       => $exception->getMessage(),
            'code'        => $exception->getCode(),
            'duration_ms' => round($duration * 1000, 2),
            'trace'       => $exception->getTraceAsString(),
        ]);
    }

    protected function sanitizeHeaders(array $headers): array
    {
        $sanitized = $headers;

        if (isset($sanitized['Authorization'])) {
            $sanitized['Authorization'] = '***';
        }

        return $sanitized;
    }

    public static function make(): self
    {
        return new static;
    }
}
