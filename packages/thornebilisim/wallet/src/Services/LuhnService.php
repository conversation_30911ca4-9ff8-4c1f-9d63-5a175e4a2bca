<?php

namespace Thorne\Wallet\Services;

use InvalidArgumentException;

class LuhnService
{
    public function generateCheckDigit(string $number): int
    {
        if (! ctype_digit($number)) {
            throw new InvalidArgumentException('Number must contain only digits');
        }

        $sum    = 0;
        $length = strlen($number);
        $parity = $length % 2;

        for ($i = 0; $i < $length; $i++) {
            $digit = (int) $number[$i];

            if ($i % 2 === $parity) {
                $digit *= 2;
                if ($digit > 9) {
                    $digit -= 9;
                }
            }

            $sum += $digit;
        }

        return (10 - ($sum % 10)) % 10;
    }

    public function validate(string $number): bool
    {
        if (! ctype_digit($number) || strlen($number) < 2) {
            return false;
        }

        $checkDigit         = (int) substr($number, -1);
        $numberWithoutCheck = substr($number, 0, -1);

        return $this->generateCheckDigit($numberWithoutCheck) === $checkDigit;
    }

    public function generateWithCheckDigit(string $baseNumber): string
    {
        if (! ctype_digit($baseNumber)) {
            throw new InvalidArgumentException('Base number must contain only digits');
        }

        $checkDigit = $this->generateCheckDigit($baseNumber);

        return $baseNumber.$checkDigit;
    }

    public function generateRandomNumber(int $length): string
    {
        if ($length <= 0) {
            throw new InvalidArgumentException('Length must be greater than 0');
        }

        $number = '';
        for ($i = 0; $i < $length; $i++) {

            $number .= $i === 0 ? random_int(1, 9) : random_int(0, 9);
        }

        return $number;
    }

    public function generateAccountNumber(string $currencyIsoCode, int $randomLength = 7): string
    {
        if (strlen($currencyIsoCode) !== 3 || ! ctype_digit($currencyIsoCode)) {
            throw new InvalidArgumentException('Currency ISO code must be exactly 3 digits');
        }

        if ($randomLength <= 0) {
            throw new InvalidArgumentException('Random length must be greater than 0');
        }

        $randomPart = $this->generateRandomNumber($randomLength);

        $baseNumber = $currencyIsoCode.$randomPart;

        $checkDigit = $this->generateCheckDigit($baseNumber);

        return $currencyIsoCode.$checkDigit.$randomPart;
    }

    public function validateAccountNumber(string $accountNumber, ?string $expectedCurrency = null): bool
    {

        if (! ctype_digit($accountNumber) || strlen($accountNumber) !== 11) {
            return false;
        }

        $currencyCode = substr($accountNumber, 0, 3);
        $checkDigit   = substr($accountNumber, 3, 1);
        $randomPart   = substr($accountNumber, 4, 7);

        if ($expectedCurrency !== null && $currencyCode !== $expectedCurrency) {
            return false;
        }

        $baseNumber = $currencyCode.$randomPart;

        return $this->generateCheckDigit($baseNumber) === (int) $checkDigit;
    }

    public function extractCurrencyCode(string $accountNumber): ?string
    {
        if (! ctype_digit($accountNumber) || strlen($accountNumber) !== 11) {
            return null;
        }

        return substr($accountNumber, 0, 3);
    }

    public function extractCheckDigit(string $accountNumber): ?int
    {
        if (! ctype_digit($accountNumber) || strlen($accountNumber) !== 11) {
            return null;
        }

        return (int) substr($accountNumber, 3, 1);
    }

    public function extractRandomPart(string $accountNumber): ?string
    {
        if (! ctype_digit($accountNumber) || strlen($accountNumber) !== 11) {
            return null;
        }

        return substr($accountNumber, 4, 7);
    }
}
