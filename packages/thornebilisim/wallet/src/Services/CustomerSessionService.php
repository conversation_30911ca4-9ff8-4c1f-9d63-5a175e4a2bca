<?php

namespace Thorne\Wallet\Services;

use Thorne\Wallet\Exceptions\CustomerNotFoundException;
use Webkul\Customer\Models\Customer;

class CustomerSessionService
{
    public function resolve(): Customer|CustomerNotFoundException|null
    {
        try {
            $customerId = request()->secureContext('customer_id');

            if ($customerId) {
                return Customer::find($customerId);
            }

            return null;
        } catch (CustomerNotFoundException $exception) {
            return $exception;
        }
    }

    public function getCustomerOrFail(): ?Customer
    {
        return $this->resolve();
    }
}
