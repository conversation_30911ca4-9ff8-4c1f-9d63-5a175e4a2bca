<?php

namespace Thorne\Wallet\Services;

use Exception;
use Illuminate\Support\Facades\DB;
use Thorne\Wallet\Enums\TransactionStatus;
use Thorne\Wallet\Enums\TransactionType;
use Thorne\Wallet\Exceptions\WalletBalanceException;
use Thorne\Wallet\Exceptions\WalletOperationException;
use Thorne\Wallet\Models\WalletAccount;
use Thorne\Wallet\Models\WalletBalance;
use Thorne\Wallet\Models\WalletTransaction;
use Webkul\Customer\Models\Customer;

class WalletService
{
    public function __construct(
        protected LuhnService $luhnService,
        protected BalanceService $balanceService,
        protected TransferService $transferService,
        protected PermissionService $permissionService
    ) {}

    public function initializeWallet(Customer $customer, ?array $currencies = null): array
    {
        $currencies = $currencies ?? array_keys(wallet_get_enabled_currencies());
        $accounts   = [];

        DB::beginTransaction();

        try {
            foreach ($currencies as $currency) {
                if (! wallet_is_supported_currency($currency)) {
                    continue;
                }

                $account = WalletAccount::getOrCreateForCustomer($customer->id, $currency);

                $balance = WalletBalance::getOrCreateForCustomer($customer->id, $currency);

                $accounts[$currency] = [
                    'account' => $account,
                    'balance' => $balance,
                ];
            }

            DB::commit();

            return $accounts;

        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function getWalletSummary(Customer $customer, bool $syncWithWeb3 = false): array
    {
        $accounts = $customer->walletAccounts()->active()->get();
        $summary  = [
            'customer_id'    => $customer->id,
            'total_accounts' => $accounts->count(),
            'currencies'     => [],
        ];

        foreach ($accounts as $account) {

            $balance = WalletBalance::where('customer_id', $customer->id)
                ->where('currency', $account->currency)
                ->first();

            if ($syncWithWeb3 && $balance && $balance->needsWeb3Sync()) {
                $balance = $this->balanceService->syncWithWeb3($customer->id, $account->currency);
            }

            $summary['currencies'][$account->currency] = [
                'account_number'              => $account->account_number,
                'formatted_account_number'    => $account->getFormattedAccountNumber(),
                'balance'                     => $balance ? $balance->balance : '0.********',
                'locked_balance'              => $balance ? $balance->locked_balance : '0.********',
                'available_balance'           => $balance ? $balance->getAvailableBalance() : '0.********',
                'formatted_balance'           => $balance ? $balance->getFormattedBalance() : '0.00',
                'formatted_available_balance' => $balance ? $balance->getFormattedAvailableBalance() : '0.00',
                'formatted_locked_balance'    => $balance ? $balance->getFormattedLockedBalance() : '0.00',
                'last_sync'                   => $balance?->web3_synced_at?->toISOString(),
                'is_active'                   => $account->is_active,
            ];
        }

        return $summary;
    }

    public function getCustomerBalances(int $customerId, bool $forceSync = false): array
    {
        if ($forceSync) {
            $balances = $this->balanceService->syncAllWithWeb3($customerId);
        } else {
            $balances = $this->balanceService->getAllBalances($customerId);
        }

        $result = [];

        foreach ($balances as $balance) {
            $result[$balance->currency] = [
                'currency'          => $balance->currency,
                'balance'           => $balance->balance,
                'locked_balance'    => $balance->locked_balance ?? '0.********',
                'available_balance' => $balance->getAvailableBalance(),
                'last_sync'         => $balance->web3_synced_at?->toISOString(),
                'web3_data'         => [
                    'address'         => $balance->getWeb3Address(),
                    'chain_id'        => $balance->getWeb3ChainId(),
                    'raw_balance'     => $balance->getRawWeb3Balance(),
                    'pending_balance' => $balance->getWeb3PendingBalance(),
                    'exponent'        => $balance->getCurrencyExponent(),
                    'is_stable_coin'  => $balance->isStableCoin(),
                    'is_cbdc'         => $balance->isCbdc(),
                ],
                'sync_status' => $balance->getWeb3SyncStatus(),
            ];
        }

        return $result;
    }

    public function createDeposit(array $data): WalletTransaction
    {
        $this->validateDepositData($data);

        $this->permissionService->validateOperationOrFail(
            'deposit',
            $data['customer_id'],
            (float) $data['amount'],
            $data['currency']
        );

        $account = WalletAccount::getOrCreateForCustomer($data['customer_id'], $data['currency']);

        return WalletTransaction::createDeposit([
            'customer_id'        => $data['customer_id'],
            'account_number'     => $account->account_number,
            'method'             => $data['method'],
            'currency'           => $data['currency'],
            'amount'             => $data['amount'],
            'fee'                => $data['fee']                ?? '0.********',
            'total_amount'       => $data['total_amount']       ?? $data['amount'],
            'description'        => $data['description']        ?? null,
            'metadata'           => $data['metadata']           ?? [],
            'external_reference' => $data['external_reference'] ?? null,
            'status'             => $data['status']             ?? TransactionStatus::PENDING,
        ]);
    }

    public function createWithdrawal(array $data): WalletTransaction
    {
        $this->validateWithdrawalData($data);

        $this->permissionService->validateOperationOrFail(
            'withdraw',
            $data['customer_id'],
            (float) $data['amount'],
            $data['currency']
        );

        $account = WalletAccount::where('customer_id', $data['customer_id'])
            ->where('currency', $data['currency'])
            ->active()
            ->firstOrFail();

        $balance = WalletBalance::where('customer_id', $data['customer_id'])
            ->where('currency', $data['currency'])
            ->lockForUpdate()
            ->first();

        if (! $balance) {
            throw WalletBalanceException::notFound($data['customer_id'], $data['currency']);
        }

        if (! $balance->hasSufficientBalance($data['amount'])) {
            throw WalletBalanceException::insufficientBalance(
                (float) $data['amount'],
                (float) $balance->balance,
                $data['currency']
            );
        }

        return WalletTransaction::createWithdrawal([
            'customer_id'        => $data['customer_id'],
            'account_number'     => $account->account_number,
            'method'             => $data['method'],
            'currency'           => $data['currency'],
            'amount'             => $data['amount'],
            'fee'                => $data['fee']                ?? '0.********',
            'total_amount'       => $data['total_amount']       ?? $data['amount'],
            'description'        => $data['description']        ?? null,
            'metadata'           => $data['metadata']           ?? [],
            'external_reference' => $data['external_reference'] ?? null,
            'status'             => $data['status']             ?? TransactionStatus::PENDING,
        ]);
    }

    public function processCompletedDeposit(WalletTransaction $transaction, array $data = []): bool
    {
        if ($transaction->type !== TransactionType::DEPOSIT) {
            throw WalletOperationException::transactionNotDeposit($transaction->id);
        }

        if ($transaction->isCompleted()) {
            return true;
        }

        DB::beginTransaction();

        try {

            $transaction->markAsCompleted($data);

            if (wallet_config('web3.enabled', true)) {
                $this->balanceService->syncWithWeb3($transaction->customer_id, $transaction->currency);
            }

            DB::commit();

            return true;

        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function processCompletedWithdrawal(WalletTransaction $transaction, array $data = []): bool
    {
        if ($transaction->type !== TransactionType::WITHDRAWAL) {
            throw WalletOperationException::transactionNotWithdrawal($transaction->id);
        }

        if ($transaction->isCompleted()) {
            return true;
        }

        DB::beginTransaction();

        try {

            $transaction->markAsCompleted($data);

            if (wallet_config('web3.enabled', true)) {
                $this->balanceService->syncWithWeb3($transaction->customer_id, $transaction->currency);
            }

            DB::commit();

            return true;

        } catch (Exception $exception) {
            DB::rollBack();
            throw $exception;
        }
    }

    public function getTransactionHistory(int $customerId, array $filters = [])
    {
        $query = WalletTransaction::where('customer_id', $customerId)
            ->orderBy('created_at', 'desc');

        if (isset($filters['currency'])) {
            $query->forCurrency($filters['currency']);
        }

        if (isset($filters['type'])) {
            $query->ofType($filters['type']);
        }

        if (isset($filters['status'])) {
            $query->withStatus(TransactionStatus::from($filters['status']));
        }

        if (isset($filters['method'])) {
            $query->byMethod($filters['method']);
        }

        if (isset($filters['from_date'])) {
            $query->where('created_at', '>=', $filters['from_date']);
        }

        if (isset($filters['to_date'])) {
            $query->where('created_at', '<=', $filters['to_date']);
        }

        return $query->paginate($filters['per_page'] ?? 15);
    }

    public function getAvailablePaymentMethods(int $customerId, string $operation, ?string $currency = null): array
    {
        $methods   = wallet_config('payment_methods', []);
        $available = [];

        foreach ($methods as $methodKey => $methodConfig) {
            if (! $methodConfig['enabled']) {
                continue;
            }

            $canPerformOperation = match ($operation) {
                'deposit'  => $methodConfig['can_deposit'],
                'withdraw' => $methodConfig['can_withdraw'],
                default    => false,
            };

            if (! $canPerformOperation) {
                continue;
            }

            if ($currency && ! in_array($currency, $methodConfig['supported_currencies'])) {
                continue;
            }

            $available[$methodKey] = $methodConfig;
        }

        return $available;
    }

    protected function validateDepositData(array $data): void
    {
        $required = ['customer_id', 'method', 'currency', 'amount'];

        foreach ($required as $field) {
            if (! isset($data[$field])) {
                throw WalletOperationException::missingRequiredField($field);
            }
        }

        if (! wallet_is_supported_currency($data['currency'])) {
            throw WalletOperationException::invalidCurrency($data['currency']);
        }

        if (bccomp($data['amount'], '0', 8) <= 0) {
            throw WalletOperationException::invalidAmount((float) $data['amount']);
        }

        $methodConfig = wallet_payment_method_config($data['method']);
        if (empty($methodConfig) || ! $methodConfig['enabled'] || ! $methodConfig['can_deposit']) {
            throw WalletOperationException::invalidPaymentMethod($data['method'], 'deposit');
        }
    }

    protected function validateWithdrawalData(array $data): void
    {
        $required = ['customer_id', 'method', 'currency', 'amount'];

        foreach ($required as $field) {
            if (! isset($data[$field])) {
                throw WalletOperationException::missingRequiredField($field);
            }
        }

        if (! wallet_is_supported_currency($data['currency'])) {
            throw WalletOperationException::invalidCurrency($data['currency']);
        }

        if (bccomp($data['amount'], '0', 8) <= 0) {
            throw WalletOperationException::invalidAmount((float) $data['amount']);
        }

        $methodConfig = wallet_payment_method_config($data['method']);
        if (empty($methodConfig) || ! $methodConfig['enabled'] || ! $methodConfig['can_withdraw']) {
            throw WalletOperationException::invalidPaymentMethod($data['method'], 'withdraw');
        }
    }
}
