<?php

namespace Thorne\Wallet\Http\Controllers\Api;

use Exception;
use Illuminate\Http\JsonResponse;
use Thorne\Wallet\Exceptions\WalletOperationException;
use Thorne\Wallet\Http\Controllers\Controller;
use Thorne\Wallet\Http\Requests\CheckOperationPermissionRequest;
use Thorne\Wallet\Http\Requests\ValidateOperationRequest;
use Thorne\Wallet\Services\CustomerSessionService;
use Thorne\Wallet\Services\PermissionService;
use Webkul\Customer\Models\Customer;

class PermissionController extends Controller
{
    public function __construct(
        protected PermissionService $permissionService,
        protected CustomerSessionService $customerSessionService
    ) {}

    public function showLimit(string $operation): JsonResponse
    {
        try {
            if (! in_array($operation, ['deposit', 'withdraw', 'transfer'])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Invalid operation. Must be one of: deposit, withdraw, transfer',
                ], 400);
            }

            $limits = $this->permissionService->getOperationLimits($operation);

            return response()->json([
                'success'   => true,
                'data'      => $limits,
                'operation' => $operation,
            ]);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve operation limits: '.$exception->getMessage(),
            ], 500);
        }
    }

    public function limits(): JsonResponse
    {
        try {
            $operations = ['deposit', 'withdraw', 'transfer'];
            $allLimits  = [];

            foreach ($operations as $operation) {
                $allLimits[$operation] = $this->permissionService->getOperationLimits($operation);
            }

            return response()->json([
                'success'    => true,
                'data'       => $allLimits,
                'operations' => $operations,
            ]);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve operation limits: '.$exception->getMessage(),
            ], 500);
        }
    }

    public function usageStatistics(): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();
        $currency = request()->get('currency');

        try {
            if ($currency) {
                $stats = $this->permissionService->getUsageStatistics($customer->id, $currency);
            } else {
                $stats = $this->permissionService->getUsageStatisticsForAllCurrencies($customer->id);
            }

            return response()->json([
                'success'     => true,
                'data'        => $stats,
                'customer_id' => $customer->id,
                'currency'    => $currency ?? 'all',
                'meta'        => [
                    'timestamp' => now()->toISOString(),
                    'timezone'  => config('app.timezone'),
                ],
            ]);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve usage statistics: '.$exception->getMessage(),
            ], 500);
        }
    }

    public function checkOperationPermission(CheckOperationPermissionRequest $request): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();

        try {
            $errors = $this->permissionService->validateOperation(
                $request->operation,
                $customer->id,
                $request->amount,
                $request->currency
            );

            $allowed         = empty($errors);
            $remainingLimits = $this->permissionService->getRemainingLimits(
                $customer->id,
                $request->operation,
                $request->currency
            );

            $this->permissionService->logPermissionCheck(
                $request->operation,
                $customer->id,
                $request->amount,
                $request->currency,
                $allowed,
                $errors
            );

            return response()->json([
                'success' => true,
                'data'    => [
                    'allowed'          => $allowed,
                    'errors'           => $errors,
                    'remaining_limits' => $remainingLimits,
                    'operation_limits' => $this->permissionService->getOperationLimits($request->operation),
                    'usage_statistics' => $this->permissionService->getUsageStatistics($customer->id, $request->currency)[$request->operation] ?? null,
                ],
                'operation'   => $request->operation,
                'amount'      => $request->amount,
                'currency'    => $request->currency,
                'customer_id' => $customer->id,
                'meta'        => [
                    'timestamp'  => now()->toISOString(),
                    'check_type' => 'comprehensive',
                ],
            ]);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to check operation permission: '.$exception->getMessage(),
            ], 500);
        }
    }

    public function validateOperation(ValidateOperationRequest $request): JsonResponse
    {
        $customer = $this->customerSessionService->getCustomerOrFail();

        try {
            $errors = $this->permissionService->validateOperation(
                $request->operation,
                $customer->id,
                $request->amount,
                $request->currency
            );

            $allowed = empty($errors);

            if ($allowed) {
                return response()->json([
                    'success' => true,
                    'message' => 'Operation is allowed',
                    'data'    => [
                        'allowed'   => true,
                        'operation' => $request->operation,
                        'amount'    => $request->amount,
                        'currency'  => $request->currency,
                    ],
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Operation not allowed',
                    'data'    => [
                        'allowed'   => false,
                        'errors'    => $errors,
                        'operation' => $request->operation,
                        'amount'    => $request->amount,
                        'currency'  => $request->currency,
                    ],
                ], 403);
            }

        } catch (WalletOperationException $exception) {
            return response()->json([
                'success' => false,
                'message' => $exception->getMessage(),
                'data'    => [
                    'allowed' => false,
                    'errors'  => $exception->getErrors(),
                    'context' => $exception->getContext(),
                ],
            ], 403);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to validate operation: '.$exception->getMessage(),
            ], 500);
        }
    }

    public function summary(): JsonResponse
    {
        try {
            $operations = ['deposit', 'withdraw', 'transfer'];
            $summary    = [];

            foreach ($operations as $operation) {
                $summary[$operation] = [
                    'enabled'          => $this->permissionService->isOperationEnabled($operation),
                    'require_kyc'      => $this->permissionService->isKycRequired($operation),
                    'require_approval' => $this->permissionService->isApprovalRequired($operation),
                    'limits_enabled'   => [
                        'daily'   => $this->permissionService->isDailyLimitEnabled($operation),
                        'monthly' => $this->permissionService->isMonthlyLimitEnabled($operation),
                        'amount'  => $this->permissionService->areAmountLimitsEnabled($operation),
                    ],
                    'limits' => [
                        'daily'      => $this->permissionService->getDailyLimit($operation),
                        'monthly'    => $this->permissionService->getMonthlyLimit($operation),
                        'min_amount' => $this->permissionService->getMinAmount($operation),
                        'max_amount' => $this->permissionService->getMaxAmount($operation),
                    ],
                ];
            }

            return response()->json([
                'success'    => true,
                'data'       => $summary,
                'operations' => $operations,
                'meta'       => [
                    'timestamp'     => now()->toISOString(),
                    'config_source' => 'wallet.permissions',
                ],
            ]);

        } catch (Exception $exception) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve permission summary: '.$exception->getMessage(),
            ], 500);
        }
    }
}
