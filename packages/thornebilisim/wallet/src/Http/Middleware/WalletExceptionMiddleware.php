<?php

namespace Thorne\Wallet\Http\Middleware;

use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Thorne\Wallet\Exceptions\WalletExceptionHandler;
use Throwable;

class WalletExceptionMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        // Her request için log yaz
        Log::info('WalletExceptionMiddleware: ALL REQUESTS', [
            'path' => $request->path(),
            'method' => $request->method()
        ]);

        // Sadece wallet API route'ları için çalış
        if (!str_starts_with($request->path(), 'api/wallet')) {
            return $next($request);
        }

        Log::info('WalletExceptionMiddleware: Processing wallet request', [
            'path' => $request->path(),
            'method' => $request->method()
        ]);

        try {
            return $next($request);
        } catch (Throwable $exception) {
            // Debug log
            Log::info('WalletExceptionMiddleware caught exception', [
                'exception' => get_class($exception),
                'message' => $exception->getMessage(),
                'path' => $request->path(),
                'should_handle' => WalletExceptionHandler::shouldHandle($exception, $request)
            ]);

            // Wallet exception handler'ı çağır
            if (WalletExceptionHandler::shouldHandle($exception, $request)) {
                $response = WalletExceptionHandler::handle($exception, $request);

                if ($response instanceof JsonResponse) {
                    Log::info('WalletExceptionHandler returned JSON response');
                    return $response;
                }
            }

            // Eğer wallet exception handler işleyemezse, exception'ı yeniden fırlat
            Log::info('Re-throwing exception as wallet handler could not process it');
            throw $exception;
        }
    }
}
