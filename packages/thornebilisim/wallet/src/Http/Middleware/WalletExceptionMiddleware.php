<?php

namespace Thorne\Wallet\Http\Middleware;

use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Thorne\Wallet\Exceptions\WalletExceptionHandler;
use Throwable;

class WalletExceptionMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        try {
            return $next($request);
        } catch (Throwable $exception) {
            if (WalletExceptionHandler::shouldHandle($exception, $request)) {
                $response = WalletExceptionHandler::handle($exception, $request);

                if ($response instanceof JsonResponse) {
                    return $response;
                }
            }

            throw $exception;
        }
    }
}
