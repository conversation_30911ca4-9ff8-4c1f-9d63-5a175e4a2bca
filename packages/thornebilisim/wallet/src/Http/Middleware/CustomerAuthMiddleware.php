<?php

namespace Thorne\Wallet\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Thorne\Wallet\Services\CustomerSessionService;

class CustomerAuthMiddleware
{
    public function __construct(
        protected CustomerSessionService $customerSession
    ) {}

    public function handle(Request $request, Closure $next)
    {
        if (! $this->customerSession->resolve()) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated',
            ], 401);
        }

        return $next($request);
    }
}
