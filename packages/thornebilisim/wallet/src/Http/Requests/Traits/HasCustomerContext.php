<?php

namespace Thorne\Wallet\Http\Requests\Traits;

use Thorne\Wallet\Services\CustomerSessionService;
use Webkul\Customer\Models\Customer;

trait HasCustomerContext
{
    protected ?Customer $customer = null;

    public function getCustomer(): Customer
    {
        if (! $this->customer) {
            $this->customer = app(CustomerSessionService::class)->getCustomerOrFail();
        }

        return $this->customer;
    }

    public function getCustomerId(): int
    {
        return $this->getCustomer()->id;
    }
}
