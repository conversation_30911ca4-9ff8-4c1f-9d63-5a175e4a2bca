<?php

namespace Thorne\Wallet\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Thorne\Wallet\Http\Requests\Traits\HasCustomerContext;

class CreateAccountRequest extends FormRequest
{
    use HasCustomerContext;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $enabledCurrencies = array_keys(wallet_get_enabled_currencies());

        return [
            'currency' => [
                'required',
                'string',
                'in:'.implode(',', $enabledCurrencies),
                function ($attribute, $value, $fail) {
                    if (! wallet_is_supported_currency(strtoupper($value))) {
                        $fail(__('wallet::app.errors.unsupported_currency'));
                    }
                },
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'currency.required' => __('wallet::app.errors.currency_required'),
            'currency.string'   => __('wallet::app.errors.currency_string'),
            'currency.in'       => __('wallet::app.errors.currency_not_supported'),
        ];
    }
}
