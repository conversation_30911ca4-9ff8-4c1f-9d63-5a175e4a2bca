<?php

namespace Thorne\Wallet\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Thorne\Wallet\Http\Requests\Traits\HasCustomerContext;
use Thorne\Wallet\Services\LimitValidationService;

class CreateDepositRequest extends FormRequest
{
    use HasCustomerContext;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'method' => [
                'required',
                'string',
                function ($attribute, $value, $fail) {
                    $methodConfig = wallet_payment_method_config($value);
                    if (empty($methodConfig) || ! $methodConfig['enabled'] || ! $methodConfig['can_deposit']) {
                        $fail(__('wallet::app.errors.method_not_supported'));
                    }
                },
            ],
            'currency' => [
                'required',
                'string',
                'between:3,4',
                function ($attribute, $value, $fail) {
                    if (! wallet_is_supported_currency(strtoupper($value))) {
                        $fail(__('wallet::app.errors.unsupported_currency'));
                    }
                },
            ],
            'amount' => [
                'required',
                'numeric',
                'min:0.01',
                function ($attribute, $value, $fail) {
                    try {
                        $limitService = app(LimitValidationService::class);
                        $limitService->validateAmountLimits(
                            'deposit',
                            $this->input('method'),
                            (float) $value,
                            strtoupper($this->input('currency', 'EUR'))
                        );
                    } catch (Exception $exception) {
                        $fail($exception->getMessage());
                    }
                },
            ],
            'description' => 'nullable|string|max:255',
            'account_id'  => 'nullable|string',
        ];
    }

    public function messages(): array
    {
        return [
            'method.required'   => __('wallet::app.deposits.select_method'),
            'currency.required' => __('wallet::app.deposits.select_currency'),
            'currency.size'     => __('wallet::app.errors.unsupported_currency'),
            'amount.required'   => __('wallet::app.deposits.enter_amount'),
            'amount.numeric'    => __('wallet::app.errors.invalid_amount'),
            'amount.min'        => __('wallet::app.errors.invalid_amount'),
        ];
    }
}
