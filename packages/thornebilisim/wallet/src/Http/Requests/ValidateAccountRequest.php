<?php

namespace Thorne\Wallet\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class ValidateAccountRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'account_number' => 'required|string|size:11',
        ];
    }

    public function messages(): array
    {
        return [
            'account_number.required' => __('wallet::app.errors.account_number_required'),
            'account_number.string'   => __('wallet::app.errors.account_number_string'),
            'account_number.size'     => __('wallet::app.errors.account_number_size'),
        ];
    }
}
