<?php

namespace Thorne\Wallet\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Thorne\Wallet\Http\Requests\Traits\HasCustomerContext;
use Thorne\Wallet\Models\WalletAccount;
use Thorne\Wallet\Services\LuhnService;

class WebTransferRequest extends FormRequest
{
    use HasCustomerContext;

    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'from_account_number' => [
                'required',
                'string',
                'size:11',
                function ($attribute, $value, $fail) {
                    $customer = $this->getCustomer();

                    $luhnService = app(LuhnService::class);
                    if (! $luhnService->validateAccountNumber($value)) {
                        $fail(__('wallet::app.errors.invalid_account_number'));

                        return;
                    }

                    $account = WalletAccount::where('account_number', $value)
                        ->where('customer_id', $customer->id)
                        ->active()
                        ->first();

                    if (! $account) {
                        $fail(__('wallet::app.errors.account_not_found'));
                    }
                },
            ],
            'to_account_number' => [
                'required',
                'string',
                'size:11',
                'different:from_account_number',
                function ($attribute, $value, $fail) {
                    $luhnService = app(LuhnService::class);
                    if (! $luhnService->validateAccountNumber($value)) {
                        $fail(__('wallet::app.errors.invalid_account_number'));

                        return;
                    }

                    $account = WalletAccount::findByAccountNumber($value);
                    if (! $account) {
                        $fail(__('wallet::app.errors.target_account_not_found'));
                    }
                },
            ],
            'amount' => [
                'required',
                'numeric',
                'min:0.01',
            ],
            'description' => 'nullable|string|max:255',
        ];
    }

    public function messages(): array
    {
        return [
            'from_account_number.required' => __('wallet::app.errors.from_account_required'),
            'from_account_number.size'     => __('wallet::app.errors.account_number_size'),
            'to_account_number.required'   => __('wallet::app.errors.to_account_required'),
            'to_account_number.size'       => __('wallet::app.errors.account_number_size'),
            'to_account_number.different'  => __('wallet::app.errors.same_account_transfer'),
            'amount.required'              => __('wallet::app.errors.amount_required'),
            'amount.numeric'               => __('wallet::app.errors.amount_numeric'),
            'amount.min'                   => __('wallet::app.errors.amount_min'),
            'description.string'           => __('wallet::app.errors.description_string'),
            'description.max'              => __('wallet::app.errors.description_max'),
        ];
    }
}
