<?php

namespace Thorne\Wallet\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static array initializeWallet(\Webkul\Customer\Models\Customer $customer, array $currencies = null)
 * @method static array getWalletOverview(\Webkul\Customer\Models\Customer $customer, bool $syncWithWeb3 = false)
 * @method static \Thorne\Wallet\Models\WalletTransaction createDeposit(array $data)
 * @method static \Thorne\Wallet\Models\WalletTransaction createWithdrawal(array $data)
 * @method static bool processCompletedDeposit(\Thorne\Wallet\Models\WalletTransaction $transaction, array $data = [])
 * @method static bool processCompletedWithdrawal(\Thorne\Wallet\Models\WalletTransaction $transaction, array $data = [])
 * @method static \Illuminate\Contracts\Pagination\LengthAwarePaginator getTransactionHistory(int $customerId, array $filters = [])
 * @method static array getAvailablePaymentMethods(int $customerId, string $operation, string|null $currency = null)
 *
 * @see \Thorne\Wallet\Services\WalletService
 */
class WalletService extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return 'wallet.service';
    }
}
