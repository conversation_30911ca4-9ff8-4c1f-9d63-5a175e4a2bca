{"name": "thornebilisim/wallet", "description": "<PERSON>im <PERSON> Yönetimi", "type": "library", "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "minimum-stability": "dev", "require": {"php": "^8.1|^8.2|^8.3", "ext-bcmath": "*", "illuminate/support": "^9.0|^10.0|^11.0", "illuminate/database": "^9.0|^10.0|^11.0", "illuminate/http": "^9.0|^10.0|^11.0", "owen-it/laravel-auditing": "^13.0"}, "require-dev": {"phpunit/phpunit": "^9.5|^10.0", "orchestra/testbench": "^7.0|^8.0|^9.0", "mockery/mockery": "^1.4"}, "autoload": {"psr-4": {"Thorne\\Wallet\\": "src/"}, "files": ["src/Support/helpers.php"]}, "extra": {"laravel": {"dont-discover": [], "providers": ["Thorne\\Wallet\\Providers\\WalletServiceProvider"], "aliases": {"WalletService": "Thorne\\Wallet\\Facades\\WalletService", "LuhnService": "Thorne\\Wallet\\Facades\\LuhnService"}}}, "scripts": {"test": "phpunit", "test-coverage": "phpunit --coverage-html coverage", "analyse": "phpstan analyse", "format": "php-cs-fixer fix"}}