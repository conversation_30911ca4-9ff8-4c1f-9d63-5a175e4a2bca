# Yapılandırma <PERSON>aları

`thornebilisim/security-layer` p<PERSON><PERSON>, <PERSON><PERSON>'in standart konfigürasyon mekanizmasını kullanarak özelleştirilebilir bir yapı sunar. Paketi projenize kurduktan ve yayınladı<PERSON>an sonra, ana yapılandırma dosyaları `config` dizininizde yer alacaktır.

## `config/security-layer.php`

<PERSON><PERSON> dos<PERSON>, gü<PERSON>lik katmanının tüm özelliklerinin ve genel davranışının ana yapılandırma merkezidir. Ortam değişkenlerinden (`.env`) değerleri okur ve her bir güvenlik özelliği için detaylı ayarlar sunar.

### Dosya İçeriği Özeti

```php
<?php

return [
    'enabled'      => env('SECURITY_LAYER_ENABLED', true),
    'cache'        => [
        'prefix' => env('SECURITY_LAYER_CACHE_PREFIX', 'security_layer:'),
        'store'  => env('SECURITY_LAYER_CACHE_STORE', 'redis'),
    ],
    'logging' => [
        'enabled' => env('SECURITY_LAYER_LOG_ENABLED', true),
        'level'   => env('SECURITY_LAYER_LOG_LEVEL', 'info'),
    ],
    'middleware_aliases' => [
        // Middleware takma adları ve sınıfları
    ],
    'features' => [
        'https' => [ /* ... HTTPS ayarları ... */ ],
        'cors' => [ /* ... CORS ayarları ... */ ],
        'nonce' => [ /* ... Nonce ayarları ... */ ],
        'timestamp' => [ /* ... Timestamp ayarları ... */ ],
        'signature' => [ /* ... İmza ayarları ... */ ],
        'rate_limit' => [ /* ... Hız sınırlama ayarları ... */ ],
        'idempotency' => [ /* ... İdempotans ayarları ... */ ],
        'session' => [ /* ... Oturum ayarları ... */ ],
        'trace' => [ /* ... İzleme ayarları ... */ ],
    ],
    'modules' => [
        'test-module' => [
            'enabled'     => true,
            'features'    => [
                // Modüle özel özellik ayarları
            ],
        ],
    ],
];
```

### Bölümler ve Amaçları

1.  **`enabled`**:
    *   Paketin genel olarak etkin olup olmadığını kontrol eder. `false` olarak ayarlanırsa, tüm güvenlik katmanı özellikleri devre dışı kalır.

2.  **`cache`**:
    *   `prefix`: Önbellekte depolanan veriler için kullanılan anahtar ön ekini belirler. Bu, diğer önbellek verileriyle çakışmayı önler.
    *   `store`: Önbellek işlemleri için kullanılacak Laravel önbellek deposunu belirtir (örneğin `redis`, `file`, `database`).

3.  **`logging`**:
    *   `enabled`: Güvenlik katmanının olayları günlüğe kaydedip kaydetmeyeceğini kontrol eder.
    *   `level`: Günlüklerin kaydedileceği minimum seviyeyi belirler (`info`, `warning`, `error` vb.).

4.  **`middleware_aliases`**:
    *   Bu bölüm, her bir güvenlik middleware'ının Laravel'in rota tanımlarında kullanılacak kısa, okunabilir takma adlarını (`alias`) tanımlar. Örneğin, `Thorne\SecurityLayer\Http\Middleware\TraceMiddleware::class` için `trace` takma adı tanımlanır. Bu takma adlar, `Route::middleware(['security-layer.trace'])` gibi kullanım kolaylığı sağlar.

5.  **`features`**:
    *   Bu, paketin sunduğu her bir güvenlik özelliğinin varsayılan ve global yapılandırmasını içerir. Her alt anahtar (örn. `https`, `cors`, `nonce`) ilgili middleware'ın davranışını kontrol eden kendi bir dizi yapılandırma değerine sahiptir.
    *   **Önemli Not**: Bu bölümde tanımlanan değerler, eğer modül bazlı veya rota üzerindeki inline konfigürasyonlar tarafından geçersiz kılınmazsa varsayılan olarak kullanılır.

6.  **`modules`**:
    *   Bu bölüm, paketin en esnek özelliklerinden biridir. Uygulamanızın belirli bölümleri veya farklı API segmentleri için özelleştirilmiş güvenlik politikaları tanımlamanıza olanak tanır.
    *   Her bir modül (`test-module` gibi), kendi `enabled` bayrağına ve modüle özel `features` bölümüne sahiptir.
    *   Bir modülün `features` bölümünde tanımlanan ayarlar, ilgili global `features` ayarlarını geçersiz kılar. Örneğin, `test-module` için `cors` ayarları, genel `features.cors` ayarlarından farklı olabilir.
    *   Bu, mikroservis mimarilerinde veya çoklu API versiyonlarını yönetirken farklı güvenlik gereksinimlerini karşılamak için idealdir.

### Yapılandırma Öncelik Sırası

`security-layer` paketi, konfigürasyon değerlerini aşağıdaki öncelik sırasına göre çözer (en yüksek öncelikli olan en alttadır):

1.  **Rota Üzerindeki Inline JSON**: `Route::middleware('security-layer.cors:{"allowed_origins":["https://inline.example.com"]}')` gibi doğrudan rota tanımında verilen JSON parametreleri. Bu, en spesifik ayardır ve diğer tüm ayarları geçersiz kılar.
2.  **Modül Bazlı Konfigürasyon**: `config/security-layer.php` dosyasındaki `modules` bölümünde tanımlanan ayarlar (örneğin `modules.my-module.features.cors`). Bu ayarlar, ilgili global özellik ayarlarını geçersiz kılar. Bir modülün etkinleştirilmesi (`enabled`) ve ilgili özelliğin modül içinde etkinleştirilmesi önemlidir.
3.  **Global Özellik Konfigürasyonu**: `config/security-layer.php` dosyasındaki `features` bölümünde tanımlanan ayarlar (örneğin `features.cors`). Bunlar, herhangi bir modül veya inline ayar yoksa varsayılan olarak kullanılır.
4.  **Ortam Değişkenleri (`.env`)**: Konfigürasyon dosyalarındaki `env()` fonksiyonları aracılığıyla okunan `.env` dosyasındaki değerler. Bu değerler, Laravel'in konfigürasyon dosyalarını önbelleğe almasından önce kullanılır.
5.  **Varsayılan Değerler**: Eğer bir ortam değişkeni veya konfigürasyon dosyasında belirli bir ayar tanımlanmamışsa, paketin kendi içindeki varsayılan değerler kullanılır.

Bu katmanlı yapı, hem kolay başlangıç yapmanızı hem de API'niz büyüdükçe veya değiştikçe güvenlik politikalarınızı esnek bir şekilde uyarlamanızı sağlar.

## `config/logging.php`

Bu dosya, Laravel'in genel günlükleme yapılandırmasının bir parçasıdır ve `security-layer` paketi için özel bir günlükleme kanalı tanımlanır.

### Dosya İçeriği Özeti

```php
<?php

return [
    'security-layer' => [
        'driver' => 'daily',
        'path'   => storage_path('logs/security-layer.log'),
        'level'  => env('SECURITY_LAYER_LOG_LEVEL', 'info'),
        'days'   => 30,
        'tap'    => [Thorne\SecurityLayer\Logging\SecurityLayerLogJson::class],
    ],
];
```

### Bölümler ve Amaçları

*   **`security-layer`**: Bu anahtar, güvenlik katmanı için tanımlanmış özel günlükleme kanalının adıdır.
    *   **`driver`**: Günlüklerin nasıl depolanacağını belirler. `daily` sürücüsü, günlüklerin günlük olarak yeni bir dosyaya yazılmasını sağlar.
    *   **`path`**: Günlük dosyalarının depolanacağı yolu belirtir. Varsayılan olarak `storage/logs/security-layer.log` altındadır.
    *   **`level`**: Bu kanal için minimum günlük seviyesini belirler. `SECURITY_LAYER_LOG_LEVEL` ortam değişkeninden değerini alır.
    *   **`days`**: `daily` sürücüsü kullanılıyorsa, kaç günlük geçmiş günlük dosyasının saklanacağını belirler.
    *   **`tap`**: Monolog günlükleyici örneğine özel tapper sınıfları eklemenizi sağlar. `Thorne\SecurityLayer\Logging\SecurityLayerLogJson::class` tapper'ı, güvenlik katmanı günlüklerinin JSON formatında ve okunabilir bir ön ekle yazılmasını sağlar. Bu, günlükleri hem programatik olarak analiz etmeyi hem de insanlar tarafından kolayca okumayı kolaylaştırır.

Bu yapılandırma, güvenlik katmanının ürettiği tüm günlüklerin ayrı, yapılandırılmış ve yönetilebilir bir şekilde depolanmasını sağlar, bu da güvenlik olaylarını izlemek ve sorunları gidermek için kritik öneme sahiptir.
