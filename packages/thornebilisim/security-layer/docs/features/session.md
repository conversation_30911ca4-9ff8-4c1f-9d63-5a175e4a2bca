# Özellik: API Oturum Yönetimi (Session)

`thornebilisim/security-layer` paketinin `SessionMiddleware` özelliği ve entegre `SessionService`, API'niz için güvenli ve durumsuz (stateless) oturum yönetimi sağlar. JSON Web Token (JWT) tabanlı bu sistem, istemcilerin kimliğini doğrulamak, yetkilendirme için kullanıcı bağlamını korumak ve API'ye güvenli erişimi sürdürmek için kullanılır.

## Tanıtım

Geleneksel web uygulamalarında oturumlar genellikle sunucu tarafında depolanırken, modern RESTful API'ler genellikle durumsuzdur. Bu, her isteğin kimlik doğrulama için gerekli tüm bilgileri içermesi gerektiği anlamına gelir. JWT'ler bu ihtiyacı karşılar: sunucu tarafından imzalanmış, g<PERSON><PERSON><PERSON> bir şekilde aktarılan ve istemci tarafında depolanabilen token'lardır.

`SessionMiddleware` ve `SessionService`, bir müşteri ID'si (`X-Customer-Id`) ile bir oturum token'ı (`X-Customer-Token`) oluşturulmasını ve sonraki isteklerde bu token'ın doğrulanmasını yönetir. Token, müşterinin kimliği, oluşturulma zamanı, son kullanma tarihi, IP adresi ve kullanıcı aracısı gibi bilgileri şifrelenmiş ve imzalanmış bir şekilde içerir. Bu sayede, her istekte veritabanı sorgusuna gerek kalmadan müşterinin kimliği doğrulanabilir ve oturum güvenliği sağlanır.

## Çalışma Prensibi

API oturum yönetimi iki ana akışa ayrılır: **Oturum Oluşturma** ve **Oturum Doğrulama**.

### 1. Oturum Oluşturma (SessionController ve SessionService)

Bu akış, bir istemcinin API'den yeni bir oturum token'ı talep ettiği senaryodur.

1.  **İstek Alma**: İstemci, genellikle `/api/security-layer/session` gibi bir uç noktaya `X-Customer-Id` başlığını içeren bir `POST` isteği gönderir.
2.  **Kontrolcü İşlemesi**: `SessionController`, isteği yakalar.
    *   `X-Customer-Id` başlığının varlığını ve `isValidCustomerId` metodunu kullanarak değerin geçerliliğini kontrol eder. Eksik veya geçersizse, `SecurityLayerExceptionHandler` aracılığıyla bir hata yanıtı döndürülür.
3.  **Servis Çağrısı**: `SessionController`, oturum oluşturma mantığı için `SessionService::createSession()` metodunu çağırır.
4.  **JWT Oluşturma**: `SessionService` içinde `generateToken()` metodu, müşterinin ID'si, oluşturulma zamanı, sona erme zamanı, istemcinin IP adresi ve kullanıcı aracısı gibi bilgileri içeren bir JWT payload'ı oluşturur. Bu payload, `security-layer.features.session.secret` (varsayılan `APP_KEY`) gizli anahtarı ve `HS256` algoritması kullanılarak imzalanır ve bir token olarak döndürülür.
5.  **Oturumu Önbelleğe Kaydetme**: Oluşturulan JWT token'ı ve ilgili oturum verileri (müşteri ID, IP, kullanıcı aracısı vb.) `SessionService::storeSession()` metodu aracılığıyla Laravel'in önbellek sistemine (`security-layer.cache.store`) kaydedilir. Bu, token'ın daha sonra doğrulanması için kritik bir adımdır. Token'ın ömrü `security-layer.features.session.ttl` ile belirlenir.
6.  **Yanıt Döndürme**: Kontrolcü, oluşturulan token'ı ve sona erme zamanını içeren bir JSON yanıtı istemciye döndürür.

### 2. Oturum Doğrulama (SessionMiddleware ve SessionService)

Bu akış, bir istemcinin API'ye korumalı bir uç noktaya erişmeye çalıştığı ve önceden alınmış bir oturum token'ı kullandığı senaryodur.

1.  **Middleware Yakalama**: İstemci, sonraki API isteklerinde `X-Customer-Token` ve `X-Customer-Id` başlıklarını gönderir. Bu istekler, ilgili rotalara atanan `SessionMiddleware` tarafından yakalanır.
2.  **Konfigürasyon ve Başlık Kontrolleri**: `SessionMiddleware`, oturum özelliğinin etkin olup olmadığını ve gerekli başlıkların (`X-Customer-Token`, `X-Customer-Id`) mevcut olup olmadığını kontrol eder. Ayrıca, token'ın JWT formatına uygunluğunu `preg_match` ile basitçe doğrular. Eksik veya hatalı başlıklar için `SecurityLayerExceptionHandler` ile hata yanıtı döndürülür.
3.  **Servis Çağrısı**: Middleware, token'ın geçerliliğini ve içeriğini doğrulamak için `SessionService::validateSession()` metodunu çağırır.
4.  **JWT Çözümleme ve Doğrulama**: `SessionService` içinde `validateSession()` metodu, token'ı gizli anahtarla çözmeye (`JWT::decode()`) çalışır.
    *   Eğer çözme başarısız olursa (token bozuk, imza geçersiz vb.), geçersiz token hatası döndürülür.
    *   Token başarıyla çözüldüğünde, elde edilen payload ile önbellekte saklanan oturum verileri (`SessionService::isSessionValid()`):
        *   Token'ın süresinin dolup dolmadığı kontrol edilir.
        *   İsteği yapan IP adresi ile token oluşturulurken kaydedilen IP adresinin eşleşip eşleşmediği kontrol edilir.
        *   İsteği yapan kullanıcı aracısı (User Agent) ile token oluşturulurken kaydedilenin eşleşip eşleşmediği kontrol edilir.
        *   Bu kontrollerden herhangi biri başarısız olursa, token geçersiz kabul edilir.
5.  **Müşteri ID Eşleşmesi**: `SessionMiddleware`, JWT'den çıkarılan müşteri ID'si ile isteğin `X-Customer-Id` başlığında gönderilen müşteri ID'sinin eşleşip eşleşmediğini son bir kez kontrol eder. Eşleşmiyorsa, uyuşmazlık hatası döndürülür.
6.  **Güvenli Bağlam Oluşturma**: Tüm doğrulamalar başarılı olursa, middleware `SecureContext` trait'ini kullanarak müşterinin ID'sini, oturum token'ını ve kimlik doğrulama durumunu (`is_authenticated_by_security_layer`) isteğe ekler. Bu bilgiler, daha sonraki kontrolcüler veya uygulama mantığı tarafından `request()->secureContext('customer_id')` gibi çağrılarla erişilebilir.
7.  **İsteği İlerletme**: Doğrulanmış istek, uygulama akışına (bir sonraki middleware veya rota işleyicisi) iletilir.

## Yapılandırma

`SessionMiddleware` ve `SessionService`'in davranışı `config/security-layer.php` dosyasındaki `features.session` bölümünde yapılandırılır. Bu özellik için modül bazlı veya inline konfigürasyon desteği bulunmaz, sadece global ayarlar geçerlidir.

```php
// config/security-layer.php

'features' => [
    'session' => [
        'enabled'    => env('SECURITY_LAYER_SESSION_ENABLED', true),
        'secret'     => env('SECURITY_LAYER_SESSION_SECRET', env('APP_KEY')), // Token imzalamak için gizli anahtar
        'ttl'        => env('SECURITY_LAYER_SESSION_TTL', 3600), // Oturum ömrü (saniye)
    ],
    // ... diğer özellikler ...
],
'cache' => [ // Oturum servisinin kullandığı önbellek ayarları
    'prefix' => env('SECURITY_LAYER_CACHE_PREFIX', 'security_layer:'),
    'store'  => env('SECURITY_LAYER_CACHE_STORE', 'redis'),
],
```

*   **`enabled`**: Oturum yönetimi özelliğini etkinleştirir veya devre dışı bırakır.
*   **`secret`**: JWT token'larını imzalamak için kullanılacak gizli anahtar. Güvenlik için `APP_KEY` ile aynı olabilir veya farklı, güçlü, rastgele bir dize olmalıdır. Kesinlikle `.env` dosyasında saklanmalıdır.
*   **`ttl`**: Oluşturulan JWT token'ının ve önbellekteki oturum verisinin geçerlilik süresi (saniye). Bu süre sonunda token otomatik olarak geçersiz hale gelir.
*   **`cache.prefix`**: Oturum verilerinin önbellekte depolandığı anahtarların ön eki.
*   **`cache.store`**: Oturum verilerinin depolanacağı Laravel önbellek deposu (örn. `redis`, `database`, `file`). Redis gibi dayanıklı ve hızlı bir önbellek deposu önerilir.

## Yaşam Döngüsü

Aşağıdaki şema, API oturum oluşturma ve doğrulama akışının yaşam döngüsünü göstermektedir. Diagram sözdiziminden kaynaklanan kutu ve karar düğümü parantezleri hariç, tüm metinler Türkçe karakterlerle ve doğru soru ekleri ile yazılmıştır.

```mermaid
graph TD
    subgraph Oturum Oluşturma Akışı
        A[İstemci POST İstek<br>/api/security-layer/session] --> B[SessionController.create];
        B --> C{X-Customer-Id Başlığı Mevcut mu?};
        C -- Hayır --> D[Eksik Müşteri ID Hatası Oluştur];
        C -- Evet --> E{Müşteri ID Geçerli mi?};
        E -- Hayır --> F[Geçersiz Müşteri ID Hatası Oluştur];
        E -- Evet --> G[SessionService.createSession Çağır];
        G --> H[JWT Token Oluştur<br>X-Customer-Id IP User Agent Süre];
        H --> I[Token ve Oturum Verisini<br>Önbelleğe Kaydet];
        I --> J[Token ve Süre ile Yanıt Döndür];
    end

    subgraph Oturum Doğrulama Akışı
        K[İstemci API İstek<br>X-Customer-Token ve X-Customer-Id ile] --> L[SessionMiddleware.handle];
        L --> M{Konfigürasyon: Oturum Etkin mi?};
        M -- Hayır --> N[İsteği Uygulama Akışına Aktar];
        M -- Evet --> O{X-Customer-Id ve X-Customer-Token Başlıkları Mevcut mu?};
        O -- Hayır --> P[Eksik Oturum Başlığı Hatası Oluştur];
        O -- Evet --> Q{Token Formatı Geçerli mi Regex?};
        Q -- Hayır --> R[Geçersiz Token Formatı Hatası Oluştur];
        Q -- Evet --> S[SessionService.validateSession Çağır];
        S --> T{Token Çözüldü ve Geçerli mi?};
        T -- Hayır --> U[Geçersiz Oturum Tokenı Hatası Oluştur];
        T -- Evet --> V{Müşteri ID'leri Eşleşiyor mu?};
        V -- Hayır --> W[Müşteri ID Uyuşmazlığı Hatası Oluştur];
        V -- Evet --> X[SecureContext ile Müşteri ID ve Tokenı Kaydet];
        X --> N;
    end

    D --> Z[Yanıtı Döndür Hata];
    F --> Z;
    P --> Z;
    R --> Z;
    U --> Z;
    W --> Z;
    J --> Z;
    N --> Z;
```

**Açıklama:**

**Oturum Oluşturma Akışı:**

1.  İstemci, yeni bir oturum token'ı almak için `SessionController.create` metodunu çağırır.
2.  Kontrolcü, `X-Customer-Id` başlığının varlığını ve geçerliliğini kontrol eder. Başarısızsa hata döndürülür.
3.  Geçerliyse, `SessionService.createSession` çağrılır.
4.  Servis, müşteri ID, IP, User Agent ve süre bilgileriyle bir JWT Token oluşturur ve bu token'ı önbelleğe kaydeder.
5.  Oluşturulan token ve sona erme süresi istemciye yanıt olarak döner.

**Oturum Doğrulama Akışı:**

1.  İstemcinin sonraki API istekleri `SessionMiddleware.handle` metoduna ulaşır.
2.  Middleware, oturum özelliğinin etkin olup olmadığını kontrol eder. Devre dışıysa istek aktarılır.
3.  Etkinse, `X-Customer-Id` ve `X-Customer-Token` başlıklarının varlığı ve token formatının geçerliliği kontrol edilir. Eksik veya hatalıysa hata döndürülür.
4.  Başarılıysa, `SessionService.validateSession` çağrılır.
5.  Servis, token'ı çözer ve önbellekteki oturum verileriyle (IP, User Agent, süre) geçerliliğini doğrular. Başarısızsa hata döndürülür.
6.  Token geçerliyse, JWT'den alınan müşteri ID'si ile istek başlığındaki müşteri ID'sinin eşleşip eşleşmediği kontrol edilir. Eşleşmiyorsa hata döndürülür.
7.  Tüm doğrulamalar başarılı olursa, müşterinin ID'si ve token'ı `secureContext` aracılığıyla isteğe kaydedilir ve istek uygulama akışına aktarılır.
8.  Her iki akışta da, bir hata oluştuğunda `SecurityLayerExceptionHandler` aracılığıyla uygun bir hata yanıtı döndürülür. Başarılı akışlar ise sonunda bir yanıt döndürür.

## Faydaları

*   **Güvenli Kimlik Doğrulama**: JWT'lerin kriptografik imzası sayesinde token'ların kurcalanmasını ve sahtekarlığını önler.
*   **Durumsuzluk (Statelessness)**: Sunucu tarafında oturum durumu tutulmadığı için API'ler daha kolay ölçeklenebilir ve yatayda genişletilebilir.
*   **Ağ Yükünü Azaltma**: Her istek için veritabanına sorgu yapma ihtiyacını ortadan kaldırır.
*   **Tek Oturum Açma (SSO) Entegrasyonu**: JWT'ler, mikroservis mimarilerinde veya birden çok uygulama arasında tek oturum açma çözümlerini uygulamak için idealdir.
*   **IP ve Kullanıcı Aracısı Eşleştirme**: Token oluşturulduğunda kaydedilen IP ve User Agent bilgilerinin sonraki isteklerde doğrulanması, token hırsızlığı durumunda bile ek bir güvenlik katmanı sağlar.
*   **Esnek Token Ömrü**: Oturumların `ttl` (Time-To-Live) ile kontrol edilebilir bir ömrü vardır, bu da güvenlik ve kullanılabilirlik arasında denge sağlar.
*   **Müşteri Bağlamı Erişimi**: Doğrulanmış müşteri ID'si ve oturum token'ı, `secureContext` aracılığıyla isteğin her yerinde kolayca erişilebilir hale gelir.

## Kullanım Örneği

`SessionMiddleware`'ı, müşteri kimlik doğrulaması gerektiren tüm API rotalarınıza atamalısınız.

**Oturum Oluşturma Rotası:**

```php
// routes/api.php
use Illuminate\Support\Facades\Route;
use Thorne\SecurityLayer\Http\Controllers\SessionController;

Route::middleware(['security-layer.cors', 'security-layer.https', 'security-layer.trace'])
    ->group(function () {
        Route::post('session', [SessionController::class, 'create'])
            ->middleware([
                'security-layer.nonce',       // Nonce doğrulaması
                'security-layer.timestamp',    // Zaman damgası doğrulaması
                'security-layer.signature',    // İstek imza doğrulaması
                'security-layer.rate_limit',   // Hız sınırlama
            ])
            ->name('session.create');
    });
```

**Korumalı API Rotası (Oturum Doğrulaması):**

```php
// routes/api.php
use Illuminate\Support\Facades\Route;

Route::middleware(['security-layer.session']) // Bu rotaya erişim için geçerli bir oturum tokenı gerekir
    ->prefix('profile')
    ->group(function () {
        Route::get('/my-details', function () {
            // SecureContext ile müşteri ID'sine erişim
            $customerId = request()->secureContext('customer_id');
            return response()->json([
                'message' => 'Profil detayları alındı',
                'customer_id' => $customerId,
            ]);
        });

        Route::post('/update-info', function () {
            $customerId = request()->secureContext('customer_id');
            // Bilgi güncelleme işlemi
            return response()->json([
                'message' => 'Bilgiler güncellendi',
                'customer_id' => $customerId,
            ]);
        });
    });
```

İstemciler, oturum oluşturma isteği için `X-Customer-Id` başlığını, sonraki korumalı istekler için ise `X-Customer-Token` ve `X-Customer-Id` başlıklarını göndermelidir.

`security-layer` paketi, `SessionMiddleware` ve `SessionService` ile API'nizin kullanıcı erişimini güvenli, ölçeklenebilir ve standartlara uygun bir şekilde yönetmenize yardımcı olur.