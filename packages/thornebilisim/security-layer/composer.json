{"name": "thornebilisim/security-layer", "description": "Thorne Bilişim API Güvenlik ve İstek Kontrol Katmanı", "type": "library", "license": "MIT", "version": "0.1.0", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.1", "firebase/php-jwt": "^6.10", "illuminate/cache": "^9.0|^10.0", "illuminate/config": "^9.0|^10.0", "illuminate/http": "^9.0|^10.0", "illuminate/routing": "^9.0|^10.0", "illuminate/support": "^9.0|^10.0", "monolog/monolog": "^2.0|^3.0"}, "autoload": {"psr-4": {"Thorne\\SecurityLayer\\": "src/"}, "files": ["src/Support/helpers.php"]}, "extra": {"laravel": {"providers": ["Thorne\\SecurityLayer\\Providers\\SecurityLayerServiceProvider"], "aliases": {"SecurityLayer": "Thorne\\SecurityLayer\\Facades\\SecurityLayer"}}}, "config": {"sort-packages": true}}