<?php

namespace Thorne\SecurityLayer\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use JsonException;
use Symfony\Component\HttpFoundation\Response;
use Thorne\SecurityLayer\Exceptions\SecurityLayerExceptionHandler;
use Thorne\SecurityLayer\Traits\LogsMiddleware;
use Thorne\SecurityLayer\Traits\ResolvesModule;

class IdempotencyMiddleware
{
    use LogsMiddleware, ResolvesModule;

    protected SecurityLayerExceptionHandler $handler;

    protected string $cachePrefix;

    protected string $cacheStore;

    private ?string $moduleKey = null;

    private array $config    = [];

    public function __construct(SecurityLayerExceptionHandler $handler)
    {
        $this->handler     = $handler;
        $this->cachePrefix = config('security-layer.cache.prefix');
        $this->cacheStore  = config('security-layer.cache.store');
    }

    public function handle(Request $request, Closure $next)
    {
        $this->initialize($request);
        if ($this->shouldSkip($request)) {
            return $next($request);
        }

        if (! $this->validateHeader($request)) {
            return $this->handler->handle($request, 'idempotency', 'missing');
        }

        $cache   = Cache::store($this->cacheStore);
        $baseKey = $this->cachePrefix.'idempotency:'.$this->getKey($request);
        $lockKey = $baseKey.':in_progress';

        if ($this->isInProgress($cache, $lockKey)) {
            return $this->handler->handle($request, 'idempotency', 'in_progress');
        }

        if ($response = $this->getCachedResponse($cache, $baseKey)) {
            return $response;
        }

        $this->markInProgress($cache, $lockKey);

        $response = $next($request);

        $this->maybeCacheResponse($cache, $baseKey, $response);
        $this->clearLock($cache, $lockKey);

        return $response;
    }

    private function initialize(Request $request): void
    {
        $this->moduleKey = $this->resolveModuleKey($request, 'idempotency');
        $this->config    = $this->resolveModuleConfig($this->moduleKey, 'idempotency');
    }

    private function shouldSkip(Request $request): bool
    {
        if (! ($this->config['enabled'] ?? false)) {
            return true;
        }
        $method = strtoupper($request->method());

        return ! in_array($method, ['POST', 'PUT', 'PATCH'], true);
    }

    private function validateHeader(Request $request): bool
    {
        $headerName = $this->config['header_name'];
        $key        = $request->header($headerName);

        if (! $key) {
            $this->logWarning('idempotency', 'Header missing', [
                'module' => $this->moduleKey,
                'header' => $headerName,
            ]);

            return false;
        }

        return true;
    }

    private function getKey(Request $request): string
    {
        return $request->header($this->config['header_name']);
    }

    private function isInProgress($cache, string $lockKey): bool
    {
        if (! $cache->has($lockKey)) {
            return false;
        }
        $this->logWarning('idempotency', 'Request in progress', [
            'module'  => $this->moduleKey,
            'lockKey' => $lockKey,
        ]);

        return true;
    }

    private function getCachedResponse($cache, string $baseKey): ?Response
    {
        $data = $cache->get($baseKey);
        if (! $data) {
            return null;
        }

        $this->logInfo('idempotency', 'Returning cached response', [
            'module'  => $this->moduleKey,
            'baseKey' => $baseKey,
        ]);

        try {
            $stored = json_decode($data, true, 512, JSON_THROW_ON_ERROR);

            if (! is_array($stored) || ! isset($stored['content'], $stored['status'], $stored['headers'])) {
                $this->logWarning('idempotency', 'Invalid cached response format', [
                    'module'  => $this->moduleKey,
                    'baseKey' => $baseKey,
                ]);

                return null;
            }

            return response($stored['content'], $stored['status'])
                ->withHeaders($stored['headers']);
        } catch (JsonException $e) {
            $this->logWarning('idempotency', 'Failed to decode cached response', [
                'module'  => $this->moduleKey,
                'baseKey' => $baseKey,
                'error'   => $e->getMessage(),
            ]);

            return null;
        }
    }

    private function markInProgress($cache, string $lockKey): void
    {
        $cache->put($lockKey, true, $this->config['cache_ttl']);
    }

    private function maybeCacheResponse($cache, string $baseKey, Response $response): void
    {
        if (! in_array($response->getStatusCode(), $this->config['successful_http_codes'], true)) {
            return;
        }

        try {
            $data = [
                'content' => $response->getContent(),
                'status'  => $response->getStatusCode(),
                'headers' => $response->headers->allPreserveCaseWithoutCookies(),
            ];

            $encoded = json_encode($data, JSON_THROW_ON_ERROR);

            $cache->put(
                $baseKey,
                $encoded,
                $this->config['cache_ttl']
            );

            $this->logInfo('idempotency', 'Response cached', [
                'module'  => $this->moduleKey,
                'baseKey' => $baseKey,
            ]);
        } catch (JsonException $e) {
            $this->logWarning('idempotency', 'Failed to encode response for caching', [
                'module'  => $this->moduleKey,
                'baseKey' => $baseKey,
                'error'   => $e->getMessage(),
            ]);
        }
    }

    private function clearLock($cache, string $lockKey): void
    {
        $cache->forget($lockKey);
    }
}
