<?php

namespace Thorne\SecurityLayer\Traits;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;

trait ResolvesModule
{
    use LogsMiddleware;

    protected function resolveModuleKey(Request $request, string $feature): ?string
    {
        $route = $request->route();
        if (! $route) {
            $this->logWarning($feature, 'Route not found', [
                'path' => $request->path(),
            ]);

            return null;
        }

        $middleware = $route->middleware();
        if (empty($middleware)) {
            $this->logWarning($feature, 'No middleware found', [
                'path' => $request->path(),
            ]);

            return null;
        }

        $this->logInfo($feature, 'Resolving module key', [
            'middleware' => $middleware,
            'feature'    => $feature,
        ]);

        foreach ($middleware as $module) {
            if (str_starts_with($module, 'security-layer.')) {
                $parts = explode('.', $module);
                if (count($parts) === 3 && $parts[2] === $feature) {
                    $this->logInfo($feature, 'Module key resolved', [
                        'moduleKey'  => $parts[1],
                        'middleware' => $module,
                    ]);

                    return $parts[1];
                }
            }
        }

        foreach ($middleware as $module) {
            if (str_starts_with($module, 'security-layer.')) {
                $parts = explode('.', $module);
                if (count($parts) === 2 && $parts[1] === $feature) {
                    $this->logInfo($feature, 'Global module key resolved', [
                        'middleware' => $module,
                    ]);

                    return 'global';
                }
            }
        }

        $this->logWarning($feature, 'No matching middleware found', [
            'middleware' => $middleware,
            'feature'    => $feature,
        ]);

        return null;
    }

    protected function resolveModuleConfig(?string $moduleKey, string $feature): array
    {
        $request      = request();
        $globalConfig = Config::get("security-layer.features.{$feature}", []);

        $route        = $request->route();
        $middleware   = $route ? $route->gatherMiddleware() : [];
        $inlineConfig = null;

        $this->logInfo($feature, 'Resolving module config', [
            'moduleKey'  => $moduleKey,
            'feature'    => $feature,
            'middleware' => $middleware,
        ]);

        foreach ($middleware as $m) {
            if (preg_match('/security-layer(?:\\.[a-zA-Z0-9_-]+)?\\.'.$feature.':(.+)/', $m, $matches)) {
                $inlineConfig = json_decode($matches[1], true);
                $this->logInfo($feature, 'Inline config found', [
                    'inlineConfig' => $inlineConfig,
                ]);
                break;
            }
        }

        if (! $moduleKey) {
            $config = $inlineConfig ? array_merge($globalConfig, $inlineConfig) : $globalConfig;
            $this->logInfo($feature, 'Using global config', [
                'config' => $config,
            ]);

            return $config;
        }

        $moduleConfig = Config::get("security-layer.modules.{$moduleKey}", []);
        $this->logInfo($feature, 'Module config found', [
            'moduleKey'    => $moduleKey,
            'moduleConfig' => $moduleConfig,
        ]);

        if (empty($moduleConfig['enabled'])) {
            $config = $inlineConfig ? array_merge($globalConfig, $inlineConfig) : $globalConfig;
            $this->logInfo($feature, 'Module disabled, using global config', [
                'config' => $config,
            ]);

            return $config;
        }

        $featureConfig = $moduleConfig['features'][$feature] ?? [];
        $this->logInfo($feature, 'Feature config found', [
            'featureConfig' => $featureConfig,
        ]);

        if (empty($featureConfig['enabled'])) {
            $config = $inlineConfig ? array_merge($globalConfig, $inlineConfig) : $globalConfig;
            $this->logInfo($feature, 'Feature disabled, using global config', [
                'config' => $config,
            ]);

            return $config;
        }

        $mergedConfig = array_merge(
            $globalConfig,
            $featureConfig,
            $inlineConfig ?? []
        );

        $this->logInfo($feature, 'Using merged config', [
            'mergedConfig' => $mergedConfig,
        ]);

        return $mergedConfig;
    }
}
