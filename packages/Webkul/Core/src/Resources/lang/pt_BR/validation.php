<?php

return [
    'address'                 => 'The :attribute can only accept alpha, numeric, spaces, comma and dashes.',
    'alpha-numeric-space'     => 'The :attribute can only accept alpha, numeric and spaces.',
    'code'                    => 'O :attribute precisa ser válido.',
    'decimal'                 => 'O :attribute precisa ser válido.',
    'phone-number'            => 'The :attribute must be valid phone number.',
    'slug'                    => 'O :attribute precisa ter um slug válido.',
    'comma-seperated-integer' => 'The :attribute field must be numeric and may contain comma.',
];
