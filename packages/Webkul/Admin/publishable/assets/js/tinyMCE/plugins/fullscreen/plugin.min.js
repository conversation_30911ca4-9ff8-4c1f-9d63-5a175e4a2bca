!function(){var a={},b=function(b){for(var c=a[b],e=c.deps,f=c.defn,g=e.length,h=new Array(g),i=0;i<g;++i)h[i]=d(e[i]);var j=f.apply(null,h);if(void 0===j)throw"module ["+b+"] returned undefined";c.instance=j},c=function(b,c,d){if("string"!=typeof b)throw"module id must be a string";if(void 0===c)throw"no dependencies for "+b;if(void 0===d)throw"no definition function for "+b;a[b]={deps:c,defn:d,instance:void 0}},d=function(c){var d=a[c];if(void 0===d)throw"module ["+c+"] was undefined";return void 0===d.instance&&b(c),d.instance},e=function(a,b){for(var c=a.length,e=new Array(c),f=0;f<c;++f)e.push(d(a[f]));b.apply(null,b)},f={};f.bolt={module:{api:{define:c,require:e,demand:d}}};var g=c,h=function(a,b){g(a,[],function(){return b})};h("3",tinymce.util.Tools.resolve),g("1",["3"],function(a){return a("tinymce.dom.DOMUtils")}),g("2",["3"],function(a){return a("tinymce.PluginManager")}),g("0",["1","2"],function(a,b){var c=a.DOM;return b.add("fullscreen",function(a){function b(){var a,b,c=window,d=document,e=d.body;return e.offsetWidth&&(a=e.offsetWidth,b=e.offsetHeight),c.innerWidth&&c.innerHeight&&(a=c.innerWidth,b=c.innerHeight),{w:a,h:b}}function d(){var a=c.getViewPort();return{x:a.x,y:a.y}}function e(a){window.scrollTo(a.x,a.y)}function f(){function f(){c.setStyle(p,"height",b().h-(o.clientHeight-p.clientHeight))}var n,o,p,q,r=document.body,s=document.documentElement;m=!m,o=a.getContainer(),n=o.style,p=a.getContentAreaContainer().firstChild,q=p.style,m?(l=d(),g=q.width,h=q.height,q.width=q.height="100%",j=n.width,k=n.height,n.width=n.height="",c.addClass(r,"mce-fullscreen"),c.addClass(s,"mce-fullscreen"),c.addClass(o,"mce-fullscreen"),c.bind(window,"resize",f),f(),i=f):(q.width=g,q.height=h,j&&(n.width=j),k&&(n.height=k),c.removeClass(r,"mce-fullscreen"),c.removeClass(s,"mce-fullscreen"),c.removeClass(o,"mce-fullscreen"),c.unbind(window,"resize",i),e(l)),a.fire("FullscreenStateChanged",{state:m})}var g,h,i,j,k,l,m=!1;if(!a.settings.inline)return a.on("init",function(){a.addShortcut("Ctrl+Shift+F","",f)}),a.on("remove",function(){i&&c.unbind(window,"resize",i)}),a.addCommand("mceFullScreen",f),a.addMenuItem("fullscreen",{text:"Fullscreen",shortcut:"Ctrl+Shift+F",selectable:!0,onClick:function(){f(),a.focus()},onPostRender:function(){var b=this;a.on("FullscreenStateChanged",function(a){b.active(a.state)})},context:"view"}),a.addButton("fullscreen",{tooltip:"Fullscreen",shortcut:"Ctrl+Shift+F",onClick:f,onPostRender:function(){var b=this;a.on("FullscreenStateChanged",function(a){b.active(a.state)})}}),{isFullscreen:function(){return m}}}),function(){}}),d("0")()}();