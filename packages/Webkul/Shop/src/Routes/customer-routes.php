<?php

use Illuminate\Support\Facades\Route;
use Webkul\Customer\Http\Controllers\AccountController;
use Webkul\Customer\Http\Controllers\AddressController;
use Webkul\Customer\Http\Controllers\CouponController;
use Webkul\Customer\Http\Controllers\CustomerController;
use Webkul\Customer\Http\Controllers\ForgotPasswordController;
use Webkul\Customer\Http\Controllers\RegistrationController;
use Webkul\Customer\Http\Controllers\ResetPasswordController;
use Webkul\Customer\Http\Controllers\SessionController;
use Webkul\Customer\Http\Controllers\VerificationController;
use Webkul\Customer\Http\Controllers\WishlistController;
use Webkul\Shop\Http\Controllers\DownloadableProductController;
use Webkul\Shop\Http\Controllers\OrderController;
use Webkul\Shop\Http\Controllers\ReviewController;
use Webkul\Shop\Http\Middleware\Verification;

Route::group(['middleware' => ['web', 'locale', 'theme', 'currency']], function () {
    /**
     * Cart merger middleware. This middleware will take care of the items
     * which are deactivated at the time of buy now functionality. If somehow
     * user redirects without completing the checkout then this will merge
     * full cart.
     *
     * If some routes are not able to merge the cart, then place the route in this
     * group.
     */
    Route::group(['middleware' => ['cart.merger']], function () {
        Route::prefix('customer')->group(function () {
            /**
             * Forgot password routes.
             */
            Route::get('/forgot-password', [ForgotPasswordController::class, 'create'])->defaults('_config', [
                'view' => 'shop::customers.signup.forgot-password',
            ])->name('customer.forgot-password.create');

            Route::post('/forgot-password', [ForgotPasswordController::class, 'store'])->name('customer.forgot-password.store');

            Route::get('/reset-password/{token}', [ResetPasswordController::class, 'create'])->defaults('_config', [
                'view' => 'shop::customers.signup.reset-password',
            ])->name('customer.reset-password.create');

            Route::get('/success', [ResetPasswordController::class, 'create'])->defaults('_config', [
                'view' => 'shop::customers.signup.success',
            ])->name('customer.register.success');

            Route::post('/success', [ResetPasswordController::class, 'store'])->defaults('_config', [
                'redirect' => 'customer.register.success',
            ])->name('customer.register.success.store');

            /**
             * Login routes.
             */
            Route::get('login', [SessionController::class, 'show'])->defaults('_config', [
                'view' => 'shop::customers.session.index',
            ])->name('customer.session.index');

            Route::post('login', [SessionController::class, 'create'])->defaults('_config', [
                'redirect' => 'customer.profile.index',
            ])->name('customer.session.create');

            /**
             * Registration routes.
             */
            Route::get('register', [RegistrationController::class, 'show'])->defaults('_config', [
                'view' => 'shop::customers.signup.index',
            ])->name('customer.register.index');

            Route::post('register', [RegistrationController::class, 'create'])->defaults('_config', [
                'redirect' => 'customer.session.index',
            ])->name('customer.register.create');

            /**
             * Customer verification routes.
             */
            Route::get('/verify-account/{token}', [RegistrationController::class, 'verifyAccount'])->name('customer.verify');

            Route::get('/resend/verification/{email}', [RegistrationController::class, 'resendVerificationEmail'])->name('customer.resend.verification-email');

            /**
             * Customer authenticated routes. All the below routes only be accessible
             * if customer is authenticated.
             */
            Route::group(['middleware' => ['customer']], function () {
                /**
                 * Logout.
                 */
                Route::delete('logout', [SessionController::class, 'destroy'])->defaults('_config', [
                    'redirect' => 'customer.session.index',
                ])->name('customer.session.destroy');

                /**
                 * Wishlist.
                 */
                Route::post('wishlist/add/{id}', [WishlistController::class, 'add'])->name('customer.wishlist.add');

                Route::post('wishlist/share', [WishlistController::class, 'share'])->name('customer.wishlist.share');

                Route::get('wishlist/shared', [WishlistController::class, 'shared'])
                    ->defaults('_config', [
                        'view' => 'shop::customers.account.wishlist.wishlist-shared',
                    ])
                    ->withoutMiddleware('customer')
                    ->name('customer.wishlist.shared');

                Route::delete('wishlist/remove/{id}', [WishlistController::class, 'remove'])->name('customer.wishlist.remove');

                Route::delete('wishlist/removeall', [WishlistController::class, 'removeAll'])->name('customer.wishlist.removeall');

                Route::get('wishlist/move/{id}', [WishlistController::class, 'move'])->name('customer.wishlist.move');

                /**
                 * Customer account. All the below routes are related to
                 * customer account details.
                 */
                Route::prefix('account')->middleware([Verification::class])->group(function () {
                    /**
                     * Dashboard.
                     */
                    Route::get('index', [AccountController::class, 'index'])->defaults('_config', [
                        'view' => 'shop::customers.account.verification.index',
                    ])->name('customer.account.index');

                    /**
                     * Verification. (Identity, Email, Phone)
                     */
                    Route::get('verification', [VerificationController::class, 'index'])->defaults('_config', [
                        'view' => 'shop::customers.account.verification.index',
                    ])->name('customer.account.verification.index');

                    Route::get('verification/pending', [VerificationController::class, 'pending'])->defaults('_config', [
                        'view' => 'shop::customers.account.verification.pending',
                    ])->name('customer.account.verification.pending');

                    Route::post('verification/{type?}', [VerificationController::class, 'store'])->defaults('_config', [
                        'redirect' => 'customer.account.index',
                    ])->name('customer.account.verification.store');

                    /**
                     * Profile.
                     */
                    /** Hacı Ali MIZRAK Tarafından Gruplandı **/
                    Route::controller(CustomerController::class)->group(function () {
                        Route::get('profile', 'index')->defaults('_config', [
                            'view' => 'shop::customers.account.profile.index',
                        ])->name('customer.profile.index');
                        Route::get('institutional', 'institutional')->defaults('_config', [
                            'view' => 'shop::customers.account.profile.institutional',
                        ])->name('customer.profile.institutional');

                        Route::get('profileJS', 'indexJS')->name('customer.profile.indexJS');

                        Route::get('password', 'password')->defaults('_config', [
                            'view' => 'shop::customers.account.profile.password',
                        ])->name('customer.profile.password');

                        Route::get('profile/edit', 'edit')->defaults('_config', [
                            'view' => 'shop::customers.account.profile.edit',
                        ])->name('customer.profile.edit');

                        Route::post('profile/edit', 'update')->defaults('_config', [
                            'redirect' => 'customer.profile.index',
                        ])->name('customer.profile.store');

                        Route::post('profile/edit/email', 'updateProfile')->defaults('_config', [
                            'redirect' => 'customer.profile.index',
                        ])->name('customer.profile.updateProfile.store');

                        Route::post('profile/edit/userJS', 'updateProfileInformationJS')->name('customer.profile.updateJS');

                        Route::post('profile/edit/user-for-institutionalJS', 'updateProfileForInstitutionalJS')->name('customer.profile.update.for.institutionalJS');

                        Route::post('profile/edit/password', 'updatePassword')->name('customer.profile.updatePassword.store');

                        Route::get('password/panel/google2fa', 'passwordPanelGoogle2fa')->name('customer.profile.password.panel.google2fa');

                        Route::post('profile/destroy', 'destroy')->defaults('_config', [
                            'redirect' => 'customer.profile.index',
                        ])->name('customer.profile.destroy');

                        Route::get('delivery-point-list', 'deliveryPointList')->name('customer.delivery.point.list');
                        Route::post('delivery-point-created', 'deliveryPointCreated')->name('customer.delivery.point.created');

                    });

                    /**
                     * Addresses.
                     */
                    /** Hacı Ali MIZRAK Tarafından Gruplandı **/
                    Route::controller(AddressController::class)->group(function () {
                        Route::get('addresses', 'index')->defaults('_config', [
                            'view' => 'shop::customers.account.address.index',
                        ])->name('customer.address.index');
                        Route::get('addresses/create', 'create')->defaults('_config', [
                            'view' => 'shop::customers.account.address.create',
                        ])->name('customer.address.create');
                        Route::post('addresses/create', 'store')->defaults('_config', [
                            'view'     => 'shop::customers.account.address.address',
                            'redirect' => 'customer.address.index',
                        ])->name('customer.address.store');
                        Route::get('addresses/edit/{id}', 'edit')->defaults('_config', [
                            'view' => 'shop::customers.account.address.edit',
                        ])->name('customer.address.edit');
                        Route::put('addresses/edit/{id}', 'update')->defaults('_config', [
                            'redirect' => 'customer.address.index',
                        ])->name('customer.address.update');

                        Route::post('addresses/createjs', 'storeJS')->name('customer.address.storejs');
                        Route::put('addresses/updatejs/{id}', 'updateJS')->name('customer.address.updatejs');
                        Route::delete('addresses/delete/{id}', 'destroy')->name('address.delete');
                        Route::delete('addresses/deletejs/{id}', 'destroyJS')->name('address.deletejs');
                        Route::get('addresses-list', 'indexJS')->name('customer.address.list');
                        Route::get('countries-list', 'countriesJS')->name('customer.countries.list');
                        Route::get('addresses/editjs/{id}', 'editJS')->name('customer.address.editjs');
                        Route::get('addresses/default/{id}', 'makeDefault')->name('make.default.address');
                    });

                    /**
                     * Wishlist.
                     */
                    Route::get('wishlist', [WishlistController::class, 'index'])->defaults('_config', [
                        'view' => 'shop::customers.account.wishlist.wishlist',
                    ])->name('customer.wishlist.index');

                    Route::get('advantages', [CouponController::class, 'index'])->defaults('_config', [
                        'view' => 'shop::customers.account.advantages.index',
                    ])->name('customer.advantages.index');

                    /**
                     * Orders.
                     */
                    Route::get('/convert-to-physical', [OrderController::class, 'convertToPhysical'])->defaults('_config', [
                        'view' => 'shop::customers.account.orders.convert-to-physical',
                    ])->name('customer.convert-to-physical');

                    Route::get('/wallet', [OrderController::class, 'wallet'])->defaults('_config', [
                        'view' => 'shop::customers.account.orders.wallet',
                    ])->name('customer.wallet.index');

                    Route::get('locked-coins', [OrderController::class, 'lockedCoins'])->defaults('_config', [
                        'view' => 'shop::customers.account.orders.locked-coins',
                    ])->name('customer.locked.coins');

                    Route::get('/transfer-details', [OrderController::class, 'transferDetails'])->defaults('_config', [
                        'view' => 'shop::customers.account.orders.transfer-details',
                    ])->name('customer.transfer-details.index');

                    Route::get('orders', [OrderController::class, 'index'])->defaults('_config', [
                        'view' => 'shop::customers.account.orders.index',
                    ])->name('customer.orders.index');

                    Route::get('orders/view/{id}', [OrderController::class, 'view'])->name('customer.orders.view');

                    Route::get('orders/print/{id}', [OrderController::class, 'printInvoice'])->defaults('_config', [
                        'view' => 'shop::customers.account.orders.print',
                    ])->name('customer.orders.print');

                    Route::post('/orders/cancel/{id}', [OrderController::class, 'cancel'])->name('customer.orders.cancel');

                    /*Route::post('/orders/wallet/withdraw/google2faactivate', [OrderController::class, 'google2faActivate'])->name('customer.wallet.withdraw.google2faactivate');
                    Route::post('/orders/wallet/withdraw/google2fachecked', [OrderController::class, 'google2faChecked'])->name('customer.wallet.withdraw.google2fachecked');
                    Route::post('/orders/wallet/table/list', [OrderController::class, 'walletTableList'])->name('customer.wallet.table.list');
                    Route::post('/orders/wallet/table/transfer/detail/list', [OrderController::class, 'walletTableTransactionList'])->name('customer.wallet.table.transfer.detail.list');
                    Route::post('/orders/wallet/deposit/cash', [OrderController::class, 'depositCash'])->name('customer.wallet.deposit.cash');
                    Route::post('/orders/wallet/deposit/crypto', [OrderController::class, 'depositCrypto'])->name('customer.wallet.deposit.crypto');
                    Route::post('/orders/wallet/withdraw/cash', [OrderController::class, 'WithdrawCash'])->name('customer.wallet.withdraw.cash');
                    Route::post('/orders/wallet/withdraw/cash/start', [OrderController::class, 'WithdrawCashStart'])->name('customer.wallet.withdraw.cash.start');
                    Route::post('/orders/wallet/withdraw/cash/handler', [OrderController::class, 'withdrawCashHandler'])->name('customer.wallet.withdraw.cash.handler');
                    Route::post('/orders/wallet/withdraw/crypto', [OrderController::class, 'withdrawCrypto'])->name('customer.wallet.withdraw.crypto');
                    Route::post('/orders/wallet/withdraw/crypto/handler', [OrderController::class, 'WithdrawCryptoHandler'])->name('customer.wallet.withdraw.crypto.handler');*/

                    /** Hacı Ali MIZRAK Tarafından Gruplandı **/
                    Route::controller(OrderController::class)->group(function () {

                        Route::get('/orders/wallet/select/chains', 'walletSelectChains')->name('customer.wallet.select.chains');

                        Route::post('/orders/select/coin/list', 'selectCoinList')->name('customer.select.coin.list');
                        Route::post('/orders/wallet/locktable', 'walletLocktable')->name('customer.wallet.locktable');
                        Route::post('/orders/wallet/table/list', 'walletTableList')->name('customer.wallet.table.list');
                        Route::post('/orders/wallet/deposit/cash', 'depositCash')->name('customer.wallet.deposit.cash');
                        Route::post('/orders/wallet/withdraw/cash', 'WithdrawCash')->name('customer.wallet.withdraw.cash');
                        Route::post('/orders/wallet/deposit/crypto', 'depositCrypto')->name('customer.wallet.deposit.crypto');
                        Route::post('/orders/wallet/withdraw/crypto', 'withdrawCrypto')->name('customer.wallet.withdraw.crypto');
                        Route::post('/orders/wallet/withdraw/cash/start', 'WithdrawCashStart')->name('customer.wallet.withdraw.cash.start');
                        Route::post('/orders/wallet/withdraw/cash/handler', 'withdrawCashHandler')->name('customer.wallet.withdraw.cash.handler');
                        Route::post('/orders/wallet/withdraw/google2fachecked', 'google2faChecked')->name('customer.wallet.withdraw.google2fachecked');
                        Route::post('/orders/wallet/withdraw/crypto/handler', 'WithdrawCryptoHandler')->name('customer.wallet.withdraw.crypto.handler');
                        Route::post('/orders/wallet/withdraw/google2faactivate', 'google2faActivate')->name('customer.wallet.withdraw.google2faactivate');
                        Route::post('/orders/wallet/table/transfer/detail/list', 'walletTableTransactionList')->name('customer.wallet.table.transfer.detail.list');
                    });

                    /**
                     * Downloadable products.
                     */
                    Route::get('downloadable-products', [DownloadableProductController::class, 'index'])->defaults('_config', [
                        'view' => 'shop::customers.account.downloadable_products.index',
                    ])->name('customer.downloadable_products.index');

                    Route::get('downloadable-products/download/{id}', [DownloadableProductController::class, 'download'])->defaults('_config', [
                        'view' => 'shop::customers.account.downloadable_products.index',
                    ])->name('customer.downloadable_products.download');

                    /**
                     * Reviews.
                     */
                    Route::get('reviews', [CustomerController::class, 'reviews'])->defaults('_config', [
                        'view' => 'shop::customers.account.reviews.index',
                    ])->name('customer.reviews.index');

                    Route::delete('reviews/delete/{id}', [ReviewController::class, 'destroy'])->defaults('_config', [
                        'redirect' => 'customer.reviews.index',
                    ])->name('customer.review.delete');

                    Route::delete('reviews/all-delete', [ReviewController::class, 'deleteAll'])->defaults('_config', [
                        'redirect' => 'customer.reviews.index',
                    ])->name('customer.review.deleteall');
                });
            });
        });
    });
});
