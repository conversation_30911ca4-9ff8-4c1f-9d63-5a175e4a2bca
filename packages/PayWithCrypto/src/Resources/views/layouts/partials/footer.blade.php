<!-- footer-area -->
<footer>
    <canvas id="can"></canvas>
    <div class="footer-area">
        <div class="container" style="margin-top: 30px;">
            <div class="footer-scroll-wrap" style="display:block;position:relative;top:-30px;">
                <button class="scroll-to-target" data-target="html"><i class="fas fa-arrow-up"></i></button>
            </div>
            <div class="footer-top">
                <div class="row">
                    <div class="col-6 col-xl-3 col-lg-3 col-sm-6">
                        <div class="footer-widget wow fadeInUp" data-wow-delay=".2s" style="visibility: visible; animation-delay: 0.2s; animation-name: fadeInUp;">
                            <a href="/" class="f-logo"><img src="/doc/white-logo.png" style="max-height: 85px;" alt=""></a>
                            <div class="footer-content">
                                <h2 class="title">
                                    <a href="{{ route('shop.home.index') }}">
                                        <img
                                            src="{{ asset('assets/img/logo/white-logo.png') }}"
                                            class="logo full-img" alt="" width="150" height="50" />
                                    </a>
                                </h2>
                                <p>
                                    {!! __('velocity::app-static.footer.footer-desc') !!}
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 col-xl-3 col-lg-3 col-sm-6">
                        <div class="footer-widget  wow fadeInUp" data-wow-delay=".4s" style="visibility: visible; animation-delay: 0.4s; animation-name: fadeInUp;">
                            <h4 class="fw-title">{{ __('velocity::app-static.footer.social-section.title') }}</h4>
                            <div class="footer-content">
                                <ul class="footer-social flex-column align-items-start justify-content-start">
                                    <li><a class="round" href="https://www.instagram.com/mirumnetwork" target="_blank"><i class="fab fa-instagram"></i></a><a class="title" href="https://www.instagram.com/mirumnetwork" target="_blank">Instagram</a></li>
                                    <li><a class="round" href="https://twitter.com/MirumNetwork" target="_blank"><i class="fab fa-twitter"></i></a><a class="title" href="https://twitter.com/MirumNetwork" target="_blank">Twitter</a></li>
                                    <li><a class="round" href="https://www.youtube.com/@ThorneBilisim" target="_blank"><i class="fab fa-youtube"></i></a><a class="title" href="https://www.youtube.com/@ThorneBilisim" target="_blank">Youtube</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 col-xl-3 col-lg-3 col-sm-6">
                        <div class="footer-widget wow fadeInUp" data-wow-delay=".6s" style="visibility: visible; animation-delay: 0.6s; animation-name: fadeInUp;">
                            <h4 class="fw-title">{{ __('velocity::app-static.footer.quick-links-section.title') }}</h4>
                            <div class="footer-link">
                                <ul>
                                    <li><a href="{{ route('shop.home.index') }}">{{ __('velocity::app-static.footer.home') }}</a></li>
                                    <li><a href="{{ route('shop.productOrCategory.index', env('FEATURED_PRODUCT_SLUG', 'mirumcoin')) }}">{{ __('velocity::app-static.footer.about') }}</a></li>
                                    <li><a href="{{ route('shop.home.faq', 'faq') }}">{{ __('velocity::app-static.footer.faq') }}</a></li>
                                    <li><a href="https://credipto.com" target="_blank">{{ __('velocity::app-static.footer.credipto') }}</a></li>
                                    <li><a href="https://terramirum.com" target="_blank">{{ __('velocity::app-static.footer.marketplace') }}</a></li>
                                    <li><a href="{{ route('shop.home.contact-us') }}">{{ __('velocity::app-static.footer.contact') }}</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-6 col-xl-3 col-lg-3 col-sm-6">
                        <div class="footer-widget wow fadeInUp" data-wow-delay=".6s" style="visibility: visible; animation-delay: 0.6s; animation-name: fadeInUp;">
                            <h4 class="fw-title">{{ __('velocity::app-static.footer.contracts') }}</h4>
                            <div class="footer-link">
                                <ul>
                                    @foreach (getFooterPage() as $item)
                                        <li class="text-lg leading-6 font-terramirum font-light mb-18p">
                                            <a target="_blank" href="/page/{{ $item->translations->where('locale', app()->getLocale())->first()?->url_key }}">{{ $item->translations->where('locale', app()->getLocale())->first()?->page_title }}</a>
                                        </li>
                                    @endforeach
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-12 col-lg-12 col-md-12 row justify-center ">
                        <div class="col-md-8 col-lg-6 footer-widget wow fadeInUp" data-wow-delay=".8s" style="visibility: visible; animation-delay: 0.8s; animation-name: fadeInUp;">
                            <h4 class="fw-title">{{ __('velocity::app-static.footer.newsletter-section.title') }}</h4>
                            <div class="footer-newsletter">
                                <form action="https://alfredform.ko.com.tr/handle/WOWIe8OGQEz3IjKSf1m23EcX2Obx5OQ2ZeC6ZhnG" method="post">
                                    <input type="email" name="Subscriber" placeholder="<EMAIL>" required="">
                                    <button type="submit"><i class="fas fa-paper-plane"></i></button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="footer-bottom">
                <div class="row align-items-center">
                    <div class="col-lg-12">
                        <div class="copyright-text">
                            <p class="text-center">

                                {!! __('velocity::app-static.footer.copy-right', ['name' => 'Credipto', 'url' => 'https://credipto.com/', 'date' => date('Y')]) !!}
                            </p>
                        </div>
                    </div>
                    <!--                    <div class="col-lg-6 d-none d-sm-block">-->
                    <!--                        <div class="footer-menu">-->
                    <!--                            <ul>-->
                    <!--                                <li><a href="#">Terms and conditions</a></li>-->
                    <!--                                <li><a href="#">Privacy policy</a></li>-->
                    <!--                                <li><a href="#">Login / Signup</a></li>-->
                    <!--                            </ul>-->
                    <!--                        </div>-->
                    <!--                    </div>-->
                </div>
            </div>
        </div>
    </div>
</footer>
<!-- footer-area-end -->