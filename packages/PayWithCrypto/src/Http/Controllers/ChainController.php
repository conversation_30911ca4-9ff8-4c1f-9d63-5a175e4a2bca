<?php

namespace PayWithCrypto\Http\Controllers;

use App\Services\OAuth\ApiClientAuth10Service;
use Exception;
use Illuminate\Support\Facades\Cache;
use Webkul\Checkout\Facades\Cart;

class ChainController extends Controller
{
    public function __construct(
        protected ApiClientAuth10Service $apiServices
    ) {}

    public function listChains()
    {
        $chains = Cache::remember('api_chains', now()->addMinutes(config('app.cache_invalidate_timeout')), function () {
            $response = $this->apiServices->baseUrl('/param/chains')
                ->baseMethod('get')
                ->baseClient();

            if ($response->successful()) {
                return $response->json();
            }

            throw new Exception('Failed to fetch chains');
        });

        return response()->json($chains);
    }

    public function listCoins()
    {
        $chain = request('chainid');
        $coins = Cache::remember('api_coins_'.$chain, now()->addMinutes(config('app.cache_invalidate_timeout')), function () use ($chain) {
            $response = $this->apiServices->baseUrl('/param/coins?chainid='.$chain)
                ->baseMethod('get')
                ->baseClient();

            if ($response->successful()) {
                return $response->json();
            }

            throw new Exception('Failed to fetch coins');
        });

        return response()->json($coins);
    }

    public function getBalance()
    {
        $cart = Cart::getCart();

        if (is_null($cart)) {
            return redirect()->route('shop.checkout.onepage.index');
        }

        $cartTotal     = 0;
        $cartCurrency  = $cart->cart_currency_code;
        $cartQuantity  = (float) $cart->items_qty ?? 0;
        $walletAddress = auth()->guard('customer')->user()->wallet;

        $cryptoChain = request()->input('chain');
        $cryptoCoin  = request()->input('coin');

        foreach ($cart['items'] as $item) {
            $cartTotal += $item['total'];
        }

        $cryptoPaymentData = [
            'userId'         => auth()->guard('customer')->user()->id,
            'tenantId'       => 50,
            'amount'         => $cartTotal * pow(10, 2),
            'exponent'       => 2,
            'currency'       => 'EUR',
            'chainId'        => $cryptoChain,
            'cryptoCurrency' => $cryptoCoin,
            'gasCoinCredit'  => 1,
        ];

        $walletBalanceData = [
            'userId'   => auth()->guard('customer')->user()->id,
            'tenantId' => 50,
            'currency' => '',
            'chainId'  => $cryptoChain,
        ];

        $walletBalanceResponse = $this->apiServices->baseUrl('/wallet/balance')
            ->baseMethod('post')
            ->baseClient($walletBalanceData);

        $txCryptoPayment = [];
        try {
            $cryptoPaymentResponse = $this->apiServices->baseUrl2('/tx/cryptopayment')
                ->baseMethod('post')
                ->baseClient($cryptoPaymentData);

            if (isset($cryptoPaymentResponse) && $cryptoPaymentResponse->json('hasAvailableBalance')) {
                return response()->json([
                    'cryptoPaymentResponse' => base64_encode(json_encode($cryptoPaymentResponse->json())),
                    'isHaveBalance'         => $cryptoPaymentResponse->json('hasAvailableBalance'),
                    'walletAddress'         => isset($walletBalanceResponse['address']) ? $walletBalanceResponse['address'] : '',
                ]);
            } else {
                throw new Exception('Crypto payment failed with error: '.$cryptoPaymentResponse->body());
            }
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage(), 'walletAddress' => isset($walletBalanceResponse['address']) ? $walletBalanceResponse['address'] : '']);
        }
    }
}
