@extends('shop::layouts.default')

@inject ('productRatingHelper', 'Webkul\Product\Helpers\Review')

@php
    $channel = core()->getCurrentChannel();

    $homeSEO = $channel->home_seo;

    if (isset($homeSEO)) {
        $homeSEO = json_decode($channel->home_seo);

        $metaTitle = $homeSEO->meta_title;

        $metaDescription = $homeSEO->meta_description;

        $metaKeywords = $homeSEO->meta_keywords;
    }
@endphp

@section('page_title')
    {{ isset($metaTitle) ? $metaTitle : "" }}
@endsection

@section('head')
    @if (isset($homeSEO))
        @isset($metaTitle)
            <meta name="title" content="{{ $metaTitle }}"/>
        @endisset

        @isset($metaDescription)
            <meta name="description" content="{{ $metaDescription }}"/>
        @endisset

        @isset($metaKeywords)
            <meta name="keywords" content="{{ $metaKeywords }}"/>
        @endisset
    @endif
@endsection

@section('body')

    <!-- main-area -->
    <main class="fix">
        <!-- breadcrumb-area -->
        <section class="breadcrumb-area breadcrumb-bg">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="breadcrumb-content">
                            <h2 class="title">{!! __('velocity::app-static.transferDetail.transfer-detail-page-title-text') !!}</h2>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- breadcrumb-area-end -->
        <!-- blog-area -->
        <section class="blog-area pt-130 pb-130">
            <div class="container">
                <div class="row">
                    <div class="col-lg-3">
                        <aside class="blog-sidebar">
                            <div class="blog-widget">
                                <h2 class="bw-title">{!! __('velocity::app-static.transferDetail.transfer-detail-title-text') !!}</h2>
                                <div class="category-list">
                                    <ul>
                                        @include('shop::customers.account.left-menu')
                                        @yield('left-menu')
                                    </ul>
                                </div>
                            </div>
                        </aside>
                    </div>
                    <div class="col-lg-9">
                        <section class="contact-aera">
                            <div class="container custom-container-four">
                                <div class="contact-info-wrap-two wow fadeInLeft" data-wow-delay=".2s" style="visibility: visible; animation-delay: 0.2s; animation-name: fadeInLeft;">
                                    <h2 class="title">{!! __('velocity::app-static.transferDetail.transfer-detail-title-text') !!}</h2>
                                    <transfer-details-component
                                        id="transferDetailsComponent"
                                        data-table-title="{!! __('velocity::app-static.transferDetail.table-title') !!}"
                                        data-table-currency="{!! __('velocity::app-static.transferDetail.table-currency') !!}"
                                        data-table-balance="{!! __('velocity::app-static.transferDetail.table-balance') !!}"
                                        data-table-blocked-balance="{!! __('velocity::app-static.transferDetail.table-blocked-balance') !!}"
                                        data-table-date="{!! __('velocity::app-static.transferDetail.table-date') !!}"

                                        data-button-today-text="{!! __('velocity::app-static.transferDetail.button-today-text') !!}"
                                        data-button-thisweek-text="{!! __('velocity::app-static.transferDetail.button-thisweek-text') !!}"
                                        data-button-thismonth-text="{!! __('velocity::app-static.transferDetail.button-thismonth-text') !!}"
                                        data-button-lastthreemonth-text="{!! __('velocity::app-static.transferDetail.button-lastthreemonth-text') !!}"
                                    ></transfer-details-component>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </div>
        </section>
        <!-- blog-area-end -->
    </main>
    <!-- main-area-end -->

@endsection

@push('scripts')
    <script src="{{ asset('js/app.js') }}"></script>
@endpush