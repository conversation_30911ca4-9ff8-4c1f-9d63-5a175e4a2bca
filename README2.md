



# tokenfab-client



Blockchain-Client makes all kind of blockchain client operation like transfer coin, burn token, mint token.



Beside, It generates custodial wallet for talents joined to the Tokenfab system for their users. This wallet private keys are securely stored under google KMS.



Blockchain-Client is a microservice appplication to connect Cosmos-based tokenfab blockchains. It is supported by pure Cosmos Modules and is written in Golang.



Blockchain-Client builds a web server to recieve requests. It connects to a blockchain endpoint and executes its commands under that specific blockchain.



Blockchain-Client is modular in nature which means that the blockchain can be changed easily. Tokenfab company has a domain driven desing approach and this module is the domain of blockchain.



Blockchain-Client seamlessy operates on the basis of two core components: Handlers and Operations.



## Application Details

- Creating wallet and storing wallet secret key under kms and return its public key

- Transfering coin under any chain defined at system.

- Transfering token under any chain defined at system.

- Cosmwasm smart contract Minting token

- Cosmwasm smart contract Burning token

- Importing wallet with mnumeric key



## Folder Structure

- config: system configuration parameters

- consts: system parameters keys and their default values

- handlers : api handlers. serialize, deserialize and validate payload

- middleware : generic authentication and logging middleware for each api request

- repository: data access layer to make crud operation with postgres

- tests: unit test implementations

- types: business implementations

- utility: system utility functions

## Confluence Documentation

For the milestones and resources, please refer to  [documentation](https://globex-innovations.atlassian.net/wiki/spaces/TOKENFAB/pages/85196821/Tokenfab+Client+Microservice+Loom).





## Branching

tokenfab-clinet operates on two main branches, ```main``` and ```develop```.



```develop``` branch is the main branch for adding new fatures, bug fixes and documentations. For additions to the ```develop``` branch, a new branch must be created and a pull request should be opened.



No direct commits to ```develop``` is allowed unless it is an urgent fix.



Only ```develop``` branch can be merged into the ```main``` branch.



### Branch Naming



For new features, create a new branch with ```FEATURE-TaskID-featureName``` format (taskID is to be taken from Jira, eg. MM-123)



For Bug Fixes, create a new branch with ```BUGFIX-TaskID-bugName``` format (taskID is to be taken from Jira, eg. MM-123)



For Documentation purposes, create a new branch with ```DOCS-docName``` format



For any other miscellaneous tasks (code refactoring, folder restructuring), create a new branch with ```MISC-TaskID-taskName``` format (taskID is to be taken from Jira, eg. MM-123)


## Installing

### Pre-requisites

- Golang (version 1.19 or later)

- IDE (Visual Studio Code)





### Compiling & Running

Clone the repo and navigate to root directory.



```bash

git clone https://github.com/tokenfab/blockchain-client.git && cd tokenfab.client

```

Build application



```bash

go build main.go

```



Run application

```bash

go run main.go

```

## Code Structure



**Operation handler initialization** has four main functions: Ping Handler, Token Handler, Wallet Handler and Authentication Handler.



A generic handler operation order has four steps: Decode request body, Validate request body, Process request body and Create and forward a response.



**Example of a generic handler operation order: Mint Token**



func (tokenHandler *TokenHandler) Mint(w http.ResponseWriter, r *http.Request) {

        var mintRequest types.MintRequest

        if err := json.NewDecoder(r.Body).Decode(&mintRequest); err != nil {

               tokenHandler.l.Println(err.Error())

               http.Error(w, "Unable to decode mintRequest to json data", http.StatusInternalServerError)

               return

        }

 

        err := mintRequest.Validate()

        if err != nil {

               http.Error(w, err.Error(), http.StatusInternalServerError)

               return

        }

 

        mintResponse, err := tokenHandler.walletOperation.Mint(mintRequest)

        if err != nil {

               tokenHandler.l.Println(err.Error())

               http.Error(w, "Unable to mint token"+err.Error(), http.StatusBadRequest)

        }

 

        if err := json.NewEncoder(w).Encode(mintResponse); err != nil {

               tokenHandler.l.Println(err.Error())

               http.Error(w, "Unable to encode tokenBurn to json data", http.StatusInternalServerError)

               return

        }



}



**Decode request body**



var mintRequest types.MintRequest

        if err := json.NewDecoder(r.Body).Decode(&mintRequest); err != nil {

               tokenHandler.l.Println(err.Error())

               http.Error(w, "Unable to decode mintRequest to json data", http.StatusInternalServerError)

               return

        }



**All the request bodies are predefined under folder “types”, for instance, the mint request type is defined as follows:**



type MintRequest struct {

        TxKey    string `json:"txKey" validate:"required"`

        ChainId  string `json:"chainId" validate:"required"`

        Address  string `json:"address" validate:"required"`

        Amount   string `json:"amount" validate:"required"`

        Currency string `json:"currency" validate:"required"`

        Memo     string `json:"memo"`

}



**If the request body is not set properly, the client throws an error and terminates the request.**



**Validate request body**



err := mintRequest.Validate()

        if err != nil {

               http.Error(w, err.Error(), http.StatusInternalServerError)

               return

        }



**In this step, the handler verifies the input by cross-checking the required types and formats of the request body.**



**Process request on blockchain**



        mintResponse, err := tokenHandler.walletOperation.Mint(mintRequest)

        if err != nil {

               tokenHandler.l.Println(err.Error())

               http.Error(w, "Unable to mint token"+err.Error(), http.StatusBadRequest)

        }



**After successful validation of the request, it gets forwarded to the specified operation to be executed on the blockchain**



**Create response and forward it**



if err := json.NewEncoder(w).Encode(mintResponse); err != nil {

               tokenHandler.l.Println(err.Error())

               http.Error(w, "Unable to encode tokenBurn to json data", http.StatusInternalServerError)

               return

        }



**In this step, “mintResponse” which is returned from the blockchain is turned into a JSON object and sent back to the user.**



## Docker commands to start microservice



Build command will create a docker image with the code base



```

docker build -t blockchain-client .

#docker build -t imageName .

```



Run command will run this created docker image with define port mappings-



```

docker run -p 7070:7070 blockchain-client

#docker run -p hostPort:containerPort blockchain-client

```

Microservice will be running on defined `hostPort`.



# Apis and their results



## Getting balances of the wallets



`/wallet/balances/{wallet}`



Request



```

curl localhost:7070/wallet/balances/tfab1ftvefsacggu8pp9h9q8czpgw9uz08ymlpy9lj3

```



Response



```

[{"amount":20000000,"currency":"TFAB","precision":6},{"amount":30000000,"currency":"TTRY","precision":6}]

```



## Creating a new wallet



`/wallet`



Request



```

curl -X POST localhost:7070/wallet

```



Response



```

{"address":"tfab1ftvefsacggu8pp9h9q8czpgw9uz08ymlpy9lj3"}

```



## Transfer tfab coin



`/coin/transfer`



Request



```

curl -X POST localhost:7070/coin/transfer -d '{"fromAddress":"address1", "toAddress": "address2", "amount": 12039, "

currency":"TFAB" }'

```



Response



```

{"txnHash":"86D39D92FB458BCFE8783391EC2B493128DB70505944BC610357E80C1557C4B3"}

```



## Burn TTRY token



`/token/burn`



Request



```

curl -X POST localhost:7070/token/burn -d '{"address":"address1", "amount": 12039, "currency":"TTRY" }'

```



Response



```

{"txnHash":"86D39D92FB458BCFE8783391EC2B493128DB70505944BC610357E80C1557C4B3"}

```



## Mint TTRY token



`/token/mint`



Request



```

curl -X POST localhost:7070/token/mint -d '{"address":"address1", "amount": 12039, "currency":"TTRY" }'

```



Response



```

{"txnHash":"86D39D92FB458BCFE8783391EC2B493128DB70505944BC610357E80C1557C4B3"}

```



## Transfer TTRY token



`/token/transfer`



Request



```

curl -X POST localhost:7070/token/transfer -d '{"fromAddress":"address1", "toAddress": "address2", "amount": 12039, "

currency":"TTRY" }'

```



Response



```

{"txnHash":"86D39D92FB458BCFE8783391EC2B493128DB70505944BC610357E80C1557C4B3"}

```

## Import wallet

```

curl -X POST localhost:7070/wallet/import -d '{"talentId":"1", "accountId": "12", "mnemonic": "inmate pioneer carry afraid length copy bid just sponsor flavor twenty fever feed impose rebuild original enjoy genre awesome few knock attack welcome liquid" }'

```

## Get Block Transaction

```

curl http://localhost:7070/tx/block/500/sepolia

```