/*! tailwindcss v2.2.17 | MIT License | https://tailwindcss.com *//*! modern-normalize v1.1.0 | MIT License | https://github.com/sindresorhus/modern-normalize */

/*
Document
========
*/

/**
Use a better box model (opinionated).
*/

*,
::before,
::after {
	box-sizing: border-box;
}

/**
Use a more readable tab size (opinionated).
*/

html {
	-moz-tab-size: 4;
	-o-tab-size: 4;
	   tab-size: 4;
}

/**
1. Correct the line height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
*/

html {
	line-height: 1.15; /* 1 */
	-webkit-text-size-adjust: 100%; /* 2 */
}

/*
Sections
========
*/

/**
Remove the margin in all browsers.
*/

body {
	margin: 0;
}

/**
Improve consistency of default fonts in all browsers. (https://github.com/sindresorhus/modern-normalize/issues/3)
*/

body {
	font-family:
		system-ui,
		-apple-system, /* Firefox supports this but not yet `system-ui` */
		'Segoe UI',
		Roboto,
		Helvetica,
		Arial,
		sans-serif,
		'Apple Color Emoji',
		'Segoe UI Emoji';
}

/*
Grouping content
================
*/

/**
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
*/

hr {
	height: 0; /* 1 */
	color: inherit; /* 2 */
}

/*
Text-level semantics
====================
*/

/**
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr[title] {
	-webkit-text-decoration: underline dotted;
	        text-decoration: underline dotted;
}

/**
Add the correct font weight in Edge and Safari.
*/

b,
strong {
	font-weight: bolder;
}

/**
1. Improve consistency of default fonts in all browsers. (https://github.com/sindresorhus/modern-normalize/issues/3)
2. Correct the odd 'em' font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
	font-family:
		ui-monospace,
		SFMono-Regular,
		Consolas,
		'Liberation Mono',
		Menlo,
		monospace; /* 1 */
	font-size: 1em; /* 2 */
}

/**
Add the correct font size in all browsers.
*/

small {
	font-size: 80%;
}

/**
Prevent 'sub' and 'sup' elements from affecting the line height in all browsers.
*/

sub,
sup {
	font-size: 75%;
	line-height: 0;
	position: relative;
	vertical-align: baseline;
}

sub {
	bottom: -0.25em;
}

sup {
	top: -0.5em;
}

/*
Tabular data
============
*/

/**
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
*/

table {
	text-indent: 0; /* 1 */
	border-color: inherit; /* 2 */
}

/*
Forms
=====
*/

/**
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
*/

button,
input,
optgroup,
select,
textarea {
	font-family: inherit; /* 1 */
	font-size: 100%; /* 1 */
	line-height: 1.15; /* 1 */
	margin: 0; /* 2 */
}

/**
Remove the inheritance of text transform in Edge and Firefox.
1. Remove the inheritance of text transform in Firefox.
*/

button,
select { /* 1 */
	text-transform: none;
}

/**
Correct the inability to style clickable types in iOS and Safari.
*/

button,
[type='button'],
[type='reset'],
[type='submit'] {
	-webkit-appearance: button;
}

/**
Remove the inner border and padding in Firefox.
*/

::-moz-focus-inner {
	border-style: none;
	padding: 0;
}

/**
Restore the focus styles unset by the previous rule.
*/

:-moz-focusring {
	outline: 1px dotted ButtonText;
}

/**
Remove the additional ':invalid' styles in Firefox.
See: https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737
*/

:-moz-ui-invalid {
	box-shadow: none;
}

/**
Remove the padding so developers are not caught out when they zero out 'fieldset' elements in all browsers.
*/

legend {
	padding: 0;
}

/**
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
	vertical-align: baseline;
}

/**
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
	height: auto;
}

/**
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
	-webkit-appearance: textfield; /* 1 */
	outline-offset: -2px; /* 2 */
}

/**
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
	-webkit-appearance: none;
}

/**
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to 'inherit' in Safari.
*/

::-webkit-file-upload-button {
	-webkit-appearance: button; /* 1 */
	font: inherit; /* 2 */
}

/*
Interactive
===========
*/

/*
Add the correct display in Chrome and Safari.
*/

summary {
	display: list-item;
}/**
 * Manually forked from SUIT CSS Base: https://github.com/suitcss/base
 * A thin layer on top of normalize.css that provides a starting point more
 * suitable for web applications.
 */

/**
 * Removes the default spacing and border for appropriate elements.
 */

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

button {
  background-color: transparent;
  background-image: none;
}

fieldset {
  margin: 0;
  padding: 0;
}

ol,
ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

/**
 * Tailwind custom reset styles
 */

/**
 * 1. Use the user's configured `sans` font-family (with Tailwind's default
 *    sans-serif font stack as a fallback) as a sane default.
 * 2. Use Tailwind's default "normal" line-height so the user isn't forced
 *    to override it to ensure consistency even when using the default theme.
 */

html {
  font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 1 */
  line-height: 1.5; /* 2 */
}


/**
 * Inherit font-family and line-height from `html` so users can set them as
 * a class directly on the `html` element.
 */

body {
  font-family: inherit;
  line-height: inherit;
}

/**
 * 1. Prevent padding and border from affecting element width.
 *
 *    We used to set this in the html element and inherit from
 *    the parent element for everything else. This caused issues
 *    in shadow-dom-enhanced elements like <details> where the content
 *    is wrapped by a div with box-sizing set to `content-box`.
 *
 *    https://github.com/mozdevs/cssremedy/issues/4
 *
 *
 * 2. Allow adding a border to an element by just adding a border-width.
 *
 *    By default, the way the browser specifies that an element should have no
 *    border is by setting it's border-style to `none` in the user-agent
 *    stylesheet.
 *
 *    In order to easily add borders to elements by just setting the `border-width`
 *    property, we change the default border-style for all elements to `solid`, and
 *    use border-width to hide them instead. This way our `border` utilities only
 *    need to set the `border-width` property instead of the entire `border`
 *    shorthand, making our border utilities much more straightforward to compose.
 *
 *    https://github.com/tailwindcss/tailwindcss/pull/116
 */

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: currentColor; /* 2 */
}

/*
 * Ensure horizontal rules are visible by default
 */

hr {
  border-top-width: 1px;
}

/**
 * Undo the `border-style: none` reset that Normalize applies to images so that
 * our `border-{width}` utilities have the expected effect.
 *
 * The Normalize reset is unnecessary for us since we default the border-width
 * to 0 on all elements.
 *
 * https://github.com/tailwindcss/tailwindcss/issues/362
 */

img {
  border-style: solid;
}

textarea {
  resize: vertical;
}

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  color: #9ca3af;
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  color: #9ca3af;
}

button,
[role="button"] {
  cursor: pointer;
}

/**
 * Override legacy focus reset from Normalize with modern Firefox focus styles.
 *
 * This is actually an improvement over the new defaults in Firefox in our testing,
 * as it triggers the better focus styles even for links, which still use a dotted
 * outline in Firefox by default.
 */
 
:-moz-focusring {
	outline: auto;
}

table {
  border-collapse: collapse;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/**
 * Reset links to optimize for opt-in styling instead of
 * opt-out.
 */

a {
  color: inherit;
  text-decoration: inherit;
}

/**
 * Reset form element properties that are easy to forget to
 * style explicitly so you don't inadvertently introduce
 * styles that deviate from your design system. These styles
 * supplement a partial reset that is already applied by
 * normalize.css.
 */

button,
input,
optgroup,
select,
textarea {
  padding: 0;
  line-height: inherit;
  color: inherit;
}

/**
 * Use the configured 'mono' font family for elements that
 * are expected to be rendered with a monospace font, falling
 * back to the system monospace stack if there is no configured
 * 'mono' font family.
 */

pre,
code,
kbd,
samp {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

/**
 * 1. Make replaced elements `display: block` by default as that's
 *    the behavior you want almost all of the time. Inspired by
 *    CSS Remedy, with `svg` added as well.
 *
 *    https://github.com/mozdevs/cssremedy/issues/14
 * 
 * 2. Add `vertical-align: middle` to align replaced elements more
 *    sensibly by default when overriding `display` by adding a
 *    utility like `inline`.
 *
 *    This can trigger a poorly considered linting error in some
 *    tools but is included by design.
 * 
 *    https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210
 */

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/**
 * Constrain images and videos to the parent width and preserve
 * their intrinsic aspect ratio.
 *
 * https://github.com/mozdevs/cssremedy/issues/14
 */

img,
video {
  max-width: 100%;
  height: auto;
}

/**
 * Ensure the default browser behavior of the `hidden` attribute.
 */

[hidden] {
  display: none;
}

*, ::before, ::after {
	--tw-translate-x: 0;
	--tw-translate-y: 0;
	--tw-rotate: 0;
	--tw-skew-x: 0;
	--tw-skew-y: 0;
	--tw-scale-x: 1;
	--tw-scale-y: 1;
	--tw-transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
	--tw-border-opacity: 1;
	border-color: rgba(229, 231, 235, var(--tw-border-opacity));
	--tw-ring-offset-shadow: 0 0 #0000;
	--tw-ring-shadow: 0 0 #0000;
	--tw-shadow: 0 0 #0000;
	--tw-ring-inset: var(--tw-empty,/*!*/ /*!*/);
	--tw-ring-offset-width: 0px;
	--tw-ring-offset-color: #fff;
	--tw-ring-color: rgba(59, 130, 246, 0.5);
	--tw-ring-offset-shadow: 0 0 #0000;
	--tw-ring-shadow: 0 0 #0000;
	--tw-shadow: 0 0 #0000;
	--tw-blur: var(--tw-empty,/*!*/ /*!*/);
	--tw-brightness: var(--tw-empty,/*!*/ /*!*/);
	--tw-contrast: var(--tw-empty,/*!*/ /*!*/);
	--tw-grayscale: var(--tw-empty,/*!*/ /*!*/);
	--tw-hue-rotate: var(--tw-empty,/*!*/ /*!*/);
	--tw-invert: var(--tw-empty,/*!*/ /*!*/);
	--tw-saturate: var(--tw-empty,/*!*/ /*!*/);
	--tw-sepia: var(--tw-empty,/*!*/ /*!*/);
	--tw-drop-shadow: var(--tw-empty,/*!*/ /*!*/);
	--tw-filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
	--tw-backdrop-blur: var(--tw-empty,/*!*/ /*!*/);
	--tw-backdrop-brightness: var(--tw-empty,/*!*/ /*!*/);
	--tw-backdrop-contrast: var(--tw-empty,/*!*/ /*!*/);
	--tw-backdrop-grayscale: var(--tw-empty,/*!*/ /*!*/);
	--tw-backdrop-hue-rotate: var(--tw-empty,/*!*/ /*!*/);
	--tw-backdrop-invert: var(--tw-empty,/*!*/ /*!*/);
	--tw-backdrop-opacity: var(--tw-empty,/*!*/ /*!*/);
	--tw-backdrop-saturate: var(--tw-empty,/*!*/ /*!*/);
	--tw-backdrop-sepia: var(--tw-empty,/*!*/ /*!*/);
	--tw-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.container {
	width: 100%;
}
@media (min-width: 640px) {

	.container {
		max-width: 640px;
	}
}
@media (min-width: 710px) {

	.container {
		max-width: 710px;
	}
}
@media (min-width: 768px) {

	.container {
		max-width: 768px;
	}
}
@media (min-width: 1100px) {

	.container {
		max-width: 1100px;
	}
}
@media (min-width: 1280px) {

	.container {
		max-width: 1280px;
	}
}
@media (min-width: 1536px) {

	.container {
		max-width: 1536px;
	}
}
.sr-only {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	white-space: nowrap;
	border-width: 0;
}
.pointer-events-none {
	pointer-events: none;
}
.pointer-events-auto {
	pointer-events: auto;
}
.invisible {
	visibility: hidden;
}
.fixed {
	position: fixed;
}
.absolute {
	position: absolute;
}
.relative {
	position: relative;
}
.sticky {
	position: sticky;
}
.inset-0 {
	top: 0px;
	right: 0px;
	bottom: 0px;
	left: 0px;
}
.top-0 {
	top: 0px;
}
.left-1 {
	left: 0.25rem;
}
.right-2 {
	right: 0.5rem;
}
.bottom-0\.5 {
	bottom: 0.125rem;
}
.left-0 {
	left: 0px;
}
.bottom-0 {
	bottom: 0px;
}
.top-3 {
	top: 0.75rem;
}
.left-3 {
	left: 0.75rem;
}
.bottom-4 {
	bottom: 1rem;
}
.top-5 {
	top: 1.25rem;
}
.right-5 {
	right: 1.25rem;
}
.bottom-\[1px\] {
	bottom: 1px;
}
.top-2 {
	top: 0.5rem;
}
.right-6 {
	right: 1.5rem;
}
.bottom-px {
	bottom: 1px;
}
.left-4 {
	left: 1rem;
}
.top-1\/4 {
	top: 25%;
}
.left-20 {
	left: 5rem;
}
.right-0 {
	right: 0px;
}
.\!-right-4 {
	right: -1rem !important;
}
.\!top-\[85\%\] {
	top: 85% !important;
}
.\!left-auto {
	left: auto !important;
}
.left-2 {
	left: 0.5rem;
}
.bottom-\[15\%\] {
	bottom: 15%;
}
.top-1\/3 {
	top: 33.333333%;
}
.right-\[7\%\] {
	right: 7%;
}
.right-3 {
	right: 0.75rem;
}
.top-\[10px\] {
	top: 10px;
}
.left-\[10px\] {
	left: 10px;
}
.bottom-1 {
	bottom: 0.25rem;
}
.top-\[85\%\] {
	top: 85%;
}
.right-4 {
	right: 1rem;
}
.z-40 {
	z-index: 40;
}
.z-10 {
	z-index: 10;
}
.z-50 {
	z-index: 50;
}
.z-0 {
	z-index: 0;
}
.z-20 {
	z-index: 20;
}
.z-999 {
	z-index: 999;
}
.z-9999 {
	z-index: 9999;
}
.z-\[50\] {
	z-index: 50;
}
.col-span-7 {
	grid-column: span 7 / span 7;
}
.col-span-5 {
	grid-column: span 5 / span 5;
}
.col-auto {
	grid-column: auto;
}
.col-span-6 {
	grid-column: span 6 / span 6;
}
.float-right {
	float: right;
}
.m-0 {
	margin: 0px;
}
.m-1 {
	margin: 0.25rem;
}
.m-20 {
	margin: 5rem;
}
.mx-auto {
	margin-left: auto;
	margin-right: auto;
}
.my-4 {
	margin-top: 1rem;
	margin-bottom: 1rem;
}
.my-2 {
	margin-top: 0.5rem;
	margin-bottom: 0.5rem;
}
.my-6 {
	margin-top: 1.5rem;
	margin-bottom: 1.5rem;
}
.my-5 {
	margin-top: 1.25rem;
	margin-bottom: 1.25rem;
}
.-mx-2 {
	margin-left: -0.5rem;
	margin-right: -0.5rem;
}
.mx-1 {
	margin-left: 0.25rem;
	margin-right: 0.25rem;
}
.my-10 {
	margin-top: 2.5rem;
	margin-bottom: 2.5rem;
}
.-mx-5 {
	margin-left: -1.25rem;
	margin-right: -1.25rem;
}
.my-12 {
	margin-top: 3rem;
	margin-bottom: 3rem;
}
.my-3 {
	margin-top: 0.75rem;
	margin-bottom: 0.75rem;
}
.my-20 {
	margin-top: 5rem;
	margin-bottom: 5rem;
}
.mt-8 {
	margin-top: 2rem;
}
.mt-\[10px\] {
	margin-top: 10px;
}
.mt-4 {
	margin-top: 1rem;
}
.mt-3 {
	margin-top: 0.75rem;
}
.mb-6 {
	margin-bottom: 1.5rem;
}
.mt-1 {
	margin-top: 0.25rem;
}
.mr-2 {
	margin-right: 0.5rem;
}
.mb-4 {
	margin-bottom: 1rem;
}
.mb-2 {
	margin-bottom: 0.5rem;
}
.mt-6 {
	margin-top: 1.5rem;
}
.mt-5 {
	margin-top: 1.25rem;
}
.ml-2 {
	margin-left: 0.5rem;
}
.mt-2 {
	margin-top: 0.5rem;
}
.mb-3 {
	margin-bottom: 0.75rem;
}
.ml-4 {
	margin-left: 1rem;
}
.ml-0\.5 {
	margin-left: 0.125rem;
}
.ml-0 {
	margin-left: 0px;
}
.ml-6 {
	margin-left: 1.5rem;
}
.mb-5 {
	margin-bottom: 1.25rem;
}
.mr-1 {
	margin-right: 0.25rem;
}
.mr-8 {
	margin-right: 2rem;
}
.mt-\[50px\] {
	margin-top: 50px;
}
.mt-12 {
	margin-top: 3rem;
}
.ml-5 {
	margin-left: 1.25rem;
}
.ml-3 {
	margin-left: 0.75rem;
}
.ml-1 {
	margin-left: 0.25rem;
}
.mr-3 {
	margin-right: 0.75rem;
}
.mt-9 {
	margin-top: 2.25rem;
}
.mb-18p {
	margin-bottom: 18px;
}
.mt-18p {
	margin-top: 18px;
}
.mb-8 {
	margin-bottom: 2rem;
}
.mt-16 {
	margin-top: 4rem;
}
.mt-7 {
	margin-top: 1.75rem;
}
.mb-10 {
	margin-bottom: 2.5rem;
}
.mt-\[40px\] {
	margin-top: 40px;
}
.mb-0\.5 {
	margin-bottom: 0.125rem;
}
.mb-0 {
	margin-bottom: 0px;
}
.mb-1 {
	margin-bottom: 0.25rem;
}
.mt-0\.5 {
	margin-top: 0.125rem;
}
.mt-0 {
	margin-top: 0px;
}
.mt-46p {
	margin-top: 46px;
}
.mb-7 {
	margin-bottom: 1.75rem;
}
.mt-40p {
	margin-top: 40px;
}
.-mt-40 {
	margin-top: -10rem;
}
.mt-10 {
	margin-top: 2.5rem;
}
.ml-12 {
	margin-left: 3rem;
}
.-mt-px {
	margin-top: -1px;
}
.-ml-px {
	margin-left: -1px;
}
.mb-12 {
	margin-bottom: 3rem;
}
.mb-9 {
	margin-bottom: 2.25rem;
}
.-mt-8 {
	margin-top: -2rem;
}
.mb-14 {
	margin-bottom: 3.5rem;
}
.mr-2\.5 {
	margin-right: 0.625rem;
}
.mb-2\.5 {
	margin-bottom: 0.625rem;
}
.mb-20 {
	margin-bottom: 5rem;
}
.ml-auto {
	margin-left: auto;
}
.mr-auto {
	margin-right: auto;
}
.-mb-12 {
	margin-bottom: -3rem;
}
.\!mt-20 {
	margin-top: 5rem !important;
}
.mt-24 {
	margin-top: 6rem;
}
.mt-14 {
	margin-top: 3.5rem;
}
.mt-20 {
	margin-top: 5rem;
}
.\!mb-0 {
	margin-bottom: 0px !important;
}
.mr-14 {
	margin-right: 3.5rem;
}
.-mt-2 {
	margin-top: -0.5rem;
}
.mt-27p {
	margin-top: 27px;
}
.\!mb-8 {
	margin-bottom: 2rem !important;
}
.box-content {
	box-sizing: content-box;
}
.block {
	display: block;
}
.inline-block {
	display: inline-block;
}
.inline {
	display: inline;
}
.flex {
	display: flex;
}
.\!flex {
	display: flex !important;
}
.inline-flex {
	display: inline-flex;
}
.table {
	display: table;
}
.grid {
	display: grid;
}
.contents {
	display: contents;
}
.hidden {
	display: none;
}
.h-6\.5 {
	height: 25px;
}
.h-6 {
	height: 1.5rem;
}
.h-8 {
	height: 2rem;
}
.h-full {
	height: 100%;
}
.h-87p {
	height: 87px;
}
.h-28 {
	height: 7rem;
}
.h-55p {
	height: 55px;
}
.h-px {
	height: 1px;
}
.h-7 {
	height: 1.75rem;
}
.\!h-6 {
	height: 1.5rem !important;
}
.h-1\/2 {
	height: 50%;
}
.h-5 {
	height: 1.25rem;
}
.h-16 {
	height: 4rem;
}
.h-\[5\.5rem\] {
	height: 5.5rem;
}
.h-\[2\.5rem\] {
	height: 2.5rem;
}
.h-\[250px\] {
	height: 250px;
}
.h-11 {
	height: 2.75rem;
}
.h-4 {
	height: 1rem;
}
.h-12 {
	height: 3rem;
}
.\!h-auto {
	height: auto !important;
}
.h-14 {
	height: 3.5rem;
}
.h-80 {
	height: 20rem;
}
.h-3\/4 {
	height: 75%;
}
.h-\[40px\] {
	height: 40px;
}
.h-\[30px\] {
	height: 30px;
}
.h-\[25px\] {
	height: 25px;
}
.h-10 {
	height: 2.5rem;
}
.h-0 {
	height: 0px;
}
.max-h-310p {
	max-height: 310px;
}
.max-h-\[85px\] {
	max-height: 85px;
}
.max-h-screen {
	max-height: 100vh;
}
.max-h-72 {
	max-height: 18rem;
}
.max-h-\[200vh\] {
	max-height: 200vh;
}
.max-h-12 {
	max-height: 3rem;
}
.max-h-24 {
	max-height: 6rem;
}
.max-h-20 {
	max-height: 5rem;
}
.max-h-\[200px\] {
	max-height: 200px;
}
.\!max-h-\[220px\] {
	max-height: 220px !important;
}
.max-h-\[220px\] {
	max-height: 220px;
}
.min-h-50p {
	min-height: 50px;
}
.min-h-60p {
	min-height: 60px;
}
.min-h-screen {
	min-height: 100vh;
}
.min-h-\[50px\] {
	min-height: 50px;
}
.min-h-\[90vh\] {
	min-height: 90vh;
}
.w-full {
	width: 100%;
}
.w-7\/12 {
	width: 58.333333%;
}
.w-5\/12 {
	width: 41.666667%;
}
.w-1\/4 {
	width: 25%;
}
.w-1\/6 {
	width: 16.666667%;
}
.w-1\/2 {
	width: 50%;
}
.w-2\/12 {
	width: 16.666667%;
}
.w-6 {
	width: 1.5rem;
}
.w-10\/12 {
	width: 83.333333%;
}
.w-6\/12 {
	width: 50%;
}
.w-4\/12 {
	width: 33.333333%;
}
.w-1\/3 {
	width: 33.333333%;
}
.w-28 {
	width: 7rem;
}
.w-8 {
	width: 2rem;
}
.w-80 {
	width: 20rem;
}
.w-2\/3 {
	width: 66.666667%;
}
.w-55p {
	width: 55px;
}
.w-2\/5 {
	width: 40%;
}
.w-40 {
	width: 10rem;
}
.w-7 {
	width: 1.75rem;
}
.w-3\/12 {
	width: 25%;
}
.w-11\/12 {
	width: 91.666667%;
}
.w-1\/12 {
	width: 8.333333%;
}
.w-8\/12 {
	width: 66.666667%;
}
.w-1\/5 {
	width: 20%;
}
.w-9\/12 {
	width: 75%;
}
.\!w-6 {
	width: 1.5rem !important;
}
.w-5 {
	width: 1.25rem;
}
.w-auto {
	width: auto;
}
.w-\[5\.5rem\] {
	width: 5.5rem;
}
.w-\[2\.5rem\] {
	width: 2.5rem;
}
.w-11 {
	width: 2.75rem;
}
.w-4 {
	width: 1rem;
}
.w-12 {
	width: 3rem;
}
.w-\[25\%\] {
	width: 25%;
}
.w-\[20\%\] {
	width: 20%;
}
.w-\[15\%\] {
	width: 15%;
}
.w-16 {
	width: 4rem;
}
.w-14 {
	width: 3.5rem;
}
.w-\[40px\] {
	width: 40px;
}
.w-\[30px\] {
	width: 30px;
}
.w-\[25px\] {
	width: 25px;
}
.w-10 {
	width: 2.5rem;
}
.\!w-2\/6 {
	width: 33.333333% !important;
}
.\!w-1\/6 {
	width: 16.666667% !important;
}
.w-3\/4 {
	width: 75%;
}
.min-w-\[14rem\] {
	min-width: 14rem;
}
.min-w-\[80vw\] {
	min-width: 80vw;
}
.min-w-\[12rem\] {
	min-width: 12rem;
}
.min-w-\[200px\] {
	min-width: 200px;
}
.max-w-kbmobile {
	max-width: 82%;
}
.max-w-full {
	max-width: 100%;
}
.max-w-282p {
	max-width: 282px;
}
.max-w-70p {
	max-width: 70px;
}
.max-w-350p {
	max-width: 350px;
}
.max-w-224p {
	max-width: 224px;
}
.max-w-324p {
	max-width: 324px;
}
.max-w-xl {
	max-width: 36rem;
}
.max-w-6xl {
	max-width: 72rem;
}
.max-w-lg {
	max-width: 32rem;
}
.max-w-5xl {
	max-width: 64rem;
}
.max-w-\[350px\] {
	max-width: 350px;
}
.max-w-\[400px\] {
	max-width: 400px;
}
.max-w-\[91rem\] {
	max-width: 91rem;
}
.max-w-\[80px\] {
	max-width: 80px;
}
.max-w-\[200px\] {
	max-width: 200px;
}
.max-w-md {
	max-width: 28rem;
}
.max-w-\[100px\] {
	max-width: 100px;
}
.max-w-\[20px\] {
	max-width: 20px;
}
.max-w-\[170px\] {
	max-width: 170px;
}
.max-w-\[35px\] {
	max-width: 35px;
}
.max-w-1\/2 {
	max-width: 50%;
}
.flex-1 {
	flex: 1 1 0%;
}
.flex-shrink-0 {
	flex-shrink: 0;
}
.border-collapse {
	border-collapse: collapse;
}
.-translate-y-full {
	--tw-translate-y: -100%;
	transform: var(--tw-transform);
}
.-translate-y-24 {
	--tw-translate-y: -6rem;
	transform: var(--tw-transform);
}
.-rotate-90 {
	--tw-rotate: -90deg;
	transform: var(--tw-transform);
}
.-rotate-6 {
	--tw-rotate: -6deg;
	transform: var(--tw-transform);
}
.rotate-\[8deg\] {
	--tw-rotate: 8deg;
	transform: var(--tw-transform);
}
.transform {
	transform: var(--tw-transform);
}
@keyframes reverse-spin {

	from {
		transform: rotate(360deg);
	}
}
.animate-reverse-spin {
	animation: reverse-spin 18s linear infinite;
}
.animate-reverse-spin-slow {
	animation: reverse-spin 18s linear infinite;
}
.cursor-pointer {
	cursor: pointer;
}
.cursor-default {
	cursor: default;
}
.list-disc {
	list-style-type: disc;
}
.grid-cols-1 {
	grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-5 {
	grid-template-columns: repeat(5, minmax(0, 1fr));
}
.grid-cols-2 {
	grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-12 {
	grid-template-columns: repeat(12, minmax(0, 1fr));
}
.flex-row {
	flex-direction: row;
}
.flex-col {
	flex-direction: column;
}
.flex-col-reverse {
	flex-direction: column-reverse;
}
.flex-wrap {
	flex-wrap: wrap;
}
.content-center {
	align-content: center;
}
.items-start {
	align-items: flex-start;
}
.items-end {
	align-items: flex-end;
}
.items-center {
	align-items: center;
}
.items-stretch {
	align-items: stretch;
}
.justify-start {
	justify-content: flex-start;
}
.justify-end {
	justify-content: flex-end;
}
.justify-center {
	justify-content: center;
}
.justify-between {
	justify-content: space-between;
}
.justify-around {
	justify-content: space-around;
}
.justify-items-center {
	justify-items: center;
}
.gap-4 {
	gap: 1rem;
}
.gap-8 {
	gap: 2rem;
}
.gap-5 {
	gap: 1.25rem;
}
.gap-2 {
	gap: 0.5rem;
}
.gap-x-7 {
	-moz-column-gap: 1.75rem;
	     column-gap: 1.75rem;
}
.gap-y-14 {
	row-gap: 3.5rem;
}
.gap-x-4 {
	-moz-column-gap: 1rem;
	     column-gap: 1rem;
}
.space-x-3 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-x-reverse: 0;
	margin-right: calc(0.75rem * var(--tw-space-x-reverse));
	margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-16 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-x-reverse: 0;
	margin-right: calc(4rem * var(--tw-space-x-reverse));
	margin-left: calc(4rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-7 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(1.75rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(1.75rem * var(--tw-space-y-reverse));
}
.space-x-2 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-x-reverse: 0;
	margin-right: calc(0.5rem * var(--tw-space-x-reverse));
	margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-4 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-x-reverse: 0;
	margin-right: calc(1rem * var(--tw-space-x-reverse));
	margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-16 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(4rem * var(--tw-space-y-reverse));
}
.space-x-12 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-x-reverse: 0;
	margin-right: calc(3rem * var(--tw-space-x-reverse));
	margin-left: calc(3rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-6 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-x-reverse: 0;
	margin-right: calc(1.5rem * var(--tw-space-x-reverse));
	margin-left: calc(1.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-x-5 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-x-reverse: 0;
	margin-right: calc(1.25rem * var(--tw-space-x-reverse));
	margin-left: calc(1.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-8 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-x-reverse: 0;
	margin-right: calc(2rem * var(--tw-space-x-reverse));
	margin-left: calc(2rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-x-reverse: 0;
	margin-right: calc(0.25rem * var(--tw-space-x-reverse));
	margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-y-reverse: 0;
	margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
	margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.space-x-0 > :not([hidden]) ~ :not([hidden]) {
	--tw-space-x-reverse: 0;
	margin-right: calc(0px * var(--tw-space-x-reverse));
	margin-left: calc(0px * calc(1 - var(--tw-space-x-reverse)));
}
.divide-y > :not([hidden]) ~ :not([hidden]) {
	--tw-divide-y-reverse: 0;
	border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
	border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.self-start {
	align-self: flex-start;
}
.self-center {
	align-self: center;
}
.overflow-hidden {
	overflow: hidden;
}
.overflow-x-auto {
	overflow-x: auto;
}
.overflow-y-auto {
	overflow-y: auto;
}
.overflow-y-hidden {
	overflow-y: hidden;
}
.overflow-x-scroll {
	overflow-x: scroll;
}
.overflow-y-scroll {
	overflow-y: scroll;
}
.whitespace-nowrap {
	white-space: nowrap;
}
.break-all {
	word-break: break-all;
}
.rounded-46p {
	border-radius: 46px;
}
.rounded-50p {
	border-radius: 50px;
}
.rounded-3xl {
	border-radius: 1.5rem;
}
.rounded-xl {
	border-radius: 0.75rem;
}
.rounded-2xl {
	border-radius: 1rem;
}
.rounded-\[18px\] {
	border-radius: 18px;
}
.rounded-\[36px\] {
	border-radius: 36px;
}
.rounded-34p {
	border-radius: 34px;
}
.rounded-full {
	border-radius: 9999px;
}
.rounded-lg {
	border-radius: 0.5rem;
}
.rounded-md {
	border-radius: 0.375rem;
}
.rounded-14p {
	border-radius: 14px;
}
.rounded-10p {
	border-radius: 10px;
}
.rounded-\[14px\] {
	border-radius: 14px;
}
.rounded-54p {
	border-radius: 54px;
}
.rounded-sm {
	border-radius: 0.125rem;
}
.rounded-\[10px\] {
	border-radius: 10px;
}
.rounded-60p {
	border-radius: 60px;
}
.rounded {
	border-radius: 0.25rem;
}
.rounded-\[59px\] {
	border-radius: 59px;
}
.rounded-t-34p {
	border-top-left-radius: 34px;
	border-top-right-radius: 34px;
}
.rounded-t-54p {
	border-top-left-radius: 54px;
	border-top-right-radius: 54px;
}
.rounded-b-54p {
	border-bottom-right-radius: 54px;
	border-bottom-left-radius: 54px;
}
.rounded-t-50p {
	border-top-left-radius: 50px;
	border-top-right-radius: 50px;
}
.rounded-l-md {
	border-top-left-radius: 0.375rem;
	border-bottom-left-radius: 0.375rem;
}
.rounded-r-md {
	border-top-right-radius: 0.375rem;
	border-bottom-right-radius: 0.375rem;
}
.rounded-t-\[59px\] {
	border-top-left-radius: 59px;
	border-top-right-radius: 59px;
}
.rounded-tl-none {
	border-top-left-radius: 0px;
}
.border-0 {
	border-width: 0px;
}
.border-2 {
	border-width: 2px;
}
.border-1p {
	border-width: 1px;
}
.border-0\.25 {
	border-width: 0.25px;
}
.border {
	border-width: 1px;
}
.border-b-2 {
	border-bottom-width: 2px;
}
.border-b-4 {
	border-bottom-width: 4px;
}
.border-r-2 {
	border-right-width: 2px;
}
.border-t-2 {
	border-top-width: 2px;
}
.border-b-1p {
	border-bottom-width: 1px;
}
.border-b-\[5px\] {
	border-bottom-width: 5px;
}
.border-b-5p {
	border-bottom-width: 6px;
}
.\!border-b-2 {
	border-bottom-width: 2px !important;
}
.border-t {
	border-top-width: 1px;
}
.border-r {
	border-right-width: 1px;
}
.border-b {
	border-bottom-width: 1px;
}
.border-t-1p {
	border-top-width: 1px;
}
.border-solid {
	border-style: solid;
}
.border-dashed {
	border-style: dashed;
}
.border-\[\#C4C2C2\] {
	--tw-border-opacity: 1;
	border-color: rgba(196, 194, 194, var(--tw-border-opacity));
}
.border-bordergray {
	--tw-border-opacity: 1;
	border-color: rgba(196, 194, 194, var(--tw-border-opacity));
}
.border-terra-red {
	--tw-border-opacity: 1;
	border-color: rgba(216, 0, 0, var(--tw-border-opacity));
}
.border-terra-light-gray {
	--tw-border-opacity: 1;
	border-color: rgba(195, 195, 195, var(--tw-border-opacity));
}
.border-terra-khaki-green {
	--tw-border-opacity: 1;
	border-color: rgba(121, 145, 78, var(--tw-border-opacity));
}
.border-terra-dark-gray {
	--tw-border-opacity: 1;
	border-color: rgba(112, 112, 112, var(--tw-border-opacity));
}
.border-borderlight-gray {
	--tw-border-opacity: 1;
	border-color: rgba(222, 222, 222, var(--tw-border-opacity));
}
.border-terra-flat-gray {
	--tw-border-opacity: 1;
	border-color: rgba(209, 209, 209, var(--tw-border-opacity));
}
.border-terra-flat-gray-2 {
	--tw-border-opacity: 1;
	border-color: rgba(208, 208, 208, var(--tw-border-opacity));
}
.border-borderflat-gray {
	--tw-border-opacity: 1;
	border-color: rgba(198, 198, 198, var(--tw-border-opacity));
}
.border-terra-orange {
	--tw-border-opacity: 1;
	border-color: rgba(245, 153, 61, var(--tw-border-opacity));
}
.border-terra-bg-light-gray-2 {
	--tw-border-opacity: 1;
	border-color: rgba(180, 180, 180, var(--tw-border-opacity));
}
.border-black {
	--tw-border-opacity: 1;
	border-color: rgba(0, 0, 0, var(--tw-border-opacity));
}
.border-transparent {
	border-color: transparent;
}
.border-terra-light-soft-gray {
	--tw-border-opacity: 1;
	border-color: rgba(199, 214, 187, var(--tw-border-opacity));
}
.border-\[\#79914E\] {
	--tw-border-opacity: 1;
	border-color: rgba(121, 145, 78, var(--tw-border-opacity));
}
.border-\[\#B4B4B4\] {
	--tw-border-opacity: 1;
	border-color: rgba(180, 180, 180, var(--tw-border-opacity));
}
.border-terra-bg-light-gray {
	--tw-border-opacity: 1;
	border-color: rgba(178, 178, 178, var(--tw-border-opacity));
}
.border-borderflat-gray2 {
	--tw-border-opacity: 1;
	border-color: rgba(207, 207, 207, var(--tw-border-opacity));
}
.border-bordermid-gray {
	--tw-border-opacity: 1;
	border-color: rgba(190, 190, 190, var(--tw-border-opacity));
}
.\!border-terra-red {
	--tw-border-opacity: 1 !important;
	border-color: rgba(216, 0, 0, var(--tw-border-opacity)) !important;
}
.border-gray-200 {
	--tw-border-opacity: 1;
	border-color: rgba(229, 231, 235, var(--tw-border-opacity));
}
.border-gray-400 {
	--tw-border-opacity: 1;
	border-color: rgba(156, 163, 175, var(--tw-border-opacity));
}
.border-gray-300 {
	--tw-border-opacity: 1;
	border-color: rgba(209, 213, 219, var(--tw-border-opacity));
}
.border-\[\#caa754\] {
	--tw-border-opacity: 1;
	border-color: rgba(202, 167, 84, var(--tw-border-opacity));
}
.border-\[\#D0AA49\] {
	--tw-border-opacity: 1;
	border-color: rgba(208, 170, 73, var(--tw-border-opacity));
}
.border-gray-700 {
	--tw-border-opacity: 1;
	border-color: rgba(55, 65, 81, var(--tw-border-opacity));
}
.border-green-600 {
	--tw-border-opacity: 1;
	border-color: rgba(5, 150, 105, var(--tw-border-opacity));
}
.border-milyem {
	--tw-border-opacity: 1;
	border-color: rgba(202, 167, 84, var(--tw-border-opacity));
}
.border-terra-soft-khaki-green {
	--tw-border-opacity: 1;
	border-color: rgba(117, 139, 74, var(--tw-border-opacity));
}
.border-white {
	--tw-border-opacity: 1;
	border-color: rgba(255, 255, 255, var(--tw-border-opacity));
}
.bg-white {
	--tw-bg-opacity: 1;
	background-color: rgba(255, 255, 255, var(--tw-bg-opacity));
}
.bg-off-white-2 {
	--tw-bg-opacity: 1;
	background-color: rgba(247, 247, 247, var(--tw-bg-opacity));
}
.bg-off-white {
	--tw-bg-opacity: 1;
	background-color: rgba(245, 245, 245, var(--tw-bg-opacity));
}
.bg-terra-orange {
	--tw-bg-opacity: 1;
	background-color: rgba(245, 153, 61, var(--tw-bg-opacity));
}
.bg-terra-light-wheat {
	--tw-bg-opacity: 1;
	background-color: rgba(255, 248, 217, var(--tw-bg-opacity));
}
.bg-terra-light-khaki-green {
	--tw-bg-opacity: 1;
	background-color: rgba(219, 220, 180, var(--tw-bg-opacity));
}
.bg-terra-khaki-green {
	--tw-bg-opacity: 1;
	background-color: rgba(121, 145, 78, var(--tw-bg-opacity));
}
.bg-terra-soft-khaki-green {
	--tw-bg-opacity: 1;
	background-color: rgba(117, 139, 74, var(--tw-bg-opacity));
}
.bg-off-via-white {
	--tw-bg-opacity: 1;
	background-color: rgba(242, 242, 242, var(--tw-bg-opacity));
}
.bg-\[\#F5993D\] {
	--tw-bg-opacity: 1;
	background-color: rgba(245, 153, 61, var(--tw-bg-opacity));
}
.bg-coupon-gray {
	--tw-bg-opacity: 1;
	background-color: rgba(232, 232, 232, var(--tw-bg-opacity));
}
.bg-terra-darkorange {
	--tw-bg-opacity: 1;
	background-color: rgba(211, 128, 31, var(--tw-bg-opacity));
}
.bg-\[\#C7D6BB\] {
	--tw-bg-opacity: 1;
	background-color: rgba(199, 214, 187, var(--tw-bg-opacity));
}
.bg-terra-input-light-green {
	--tw-bg-opacity: 1;
	background-color: rgba(246, 255, 244, var(--tw-bg-opacity));
}
.bg-terra-light-red {
	--tw-bg-opacity: 1;
	background-color: rgba(255, 244, 244, var(--tw-bg-opacity));
}
.bg-gray-200 {
	--tw-bg-opacity: 1;
	background-color: rgba(229, 231, 235, var(--tw-bg-opacity));
}
.bg-border-light-green {
	--tw-bg-opacity: 1;
	background-color: rgba(229, 238, 226, var(--tw-bg-opacity));
}
.bg-terra-other-green {
	--tw-bg-opacity: 1;
	background-color: rgba(67, 125, 59, var(--tw-bg-opacity));
}
.bg-\[\#E8E8E8\] {
	--tw-bg-opacity: 1;
	background-color: rgba(232, 232, 232, var(--tw-bg-opacity));
}
.bg-terra-yellow {
	--tw-bg-opacity: 1;
	background-color: rgba(245, 236, 8, var(--tw-bg-opacity));
}
.bg-terra-bg-light-green {
	--tw-bg-opacity: 1;
	background-color: rgba(238, 241, 232, var(--tw-bg-opacity));
}
.bg-\[\#E9EFEB\] {
	--tw-bg-opacity: 1;
	background-color: rgba(233, 239, 235, var(--tw-bg-opacity));
}
.bg-terra-light-soft-gray {
	--tw-bg-opacity: 1;
	background-color: rgba(199, 214, 187, var(--tw-bg-opacity));
}
.bg-gray-100 {
	--tw-bg-opacity: 1;
	background-color: rgba(243, 244, 246, var(--tw-bg-opacity));
}
.bg-gray-300 {
	--tw-bg-opacity: 1;
	background-color: rgba(209, 213, 219, var(--tw-bg-opacity));
}
.bg-milyem {
	--tw-bg-opacity: 1;
	background-color: rgba(202, 167, 84, var(--tw-bg-opacity));
}
.bg-\[\#0e1620\] {
	--tw-bg-opacity: 1;
	background-color: rgba(14, 22, 32, var(--tw-bg-opacity));
}
.bg-\[\#caa754\] {
	--tw-bg-opacity: 1;
	background-color: rgba(202, 167, 84, var(--tw-bg-opacity));
}
.bg-terra-text-white {
	--tw-bg-opacity: 1;
	background-color: rgba(241, 246, 239, var(--tw-bg-opacity));
}
.bg-\[\#D0AA49\] {
	--tw-bg-opacity: 1;
	background-color: rgba(208, 170, 73, var(--tw-bg-opacity));
}
.bg-\[\#d8d8d8\] {
	--tw-bg-opacity: 1;
	background-color: rgba(216, 216, 216, var(--tw-bg-opacity));
}
.bg-transparent {
	background-color: transparent;
}
.bg-\[\#000000\] {
	--tw-bg-opacity: 1;
	background-color: rgba(0, 0, 0, var(--tw-bg-opacity));
}
.bg-black\/20 {
	background-color: rgba(0, 0, 0, 0.2);
}
.bg-terra-searchbar-green {
	--tw-bg-opacity: 1;
	background-color: rgba(233, 239, 235, var(--tw-bg-opacity));
}
.\!bg-terra-searchbar-green {
	--tw-bg-opacity: 1 !important;
	background-color: rgba(233, 239, 235, var(--tw-bg-opacity)) !important;
}
.bg-opacity-30 {
	--tw-bg-opacity: 0.3;
}
.bg-gradient-to-t {
	background-image: linear-gradient(to top, var(--tw-gradient-stops));
}
.bg-gradient-to-b {
	background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.from-terra-light-soft-gray {
	--tw-gradient-from: #C7D6BB;
	--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(199, 214, 187, 0));
}
.from-terra-light-blue {
	--tw-gradient-from: #E0FFFD;
	--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(224, 255, 253, 0));
}
.from-black {
	--tw-gradient-from: #000;
	--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(0, 0, 0, 0));
}
.from-\[\#0000009c\] {
	--tw-gradient-from: #0000009c;
	--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(0, 0, 0, 0));
}
.via-terra-text-white {
	--tw-gradient-stops: var(--tw-gradient-from), #F1F6EF, var(--tw-gradient-to, rgba(241, 246, 239, 0));
}
.to-transparent {
	--tw-gradient-to: transparent;
}
.bg-cover {
	background-size: cover;
}
.bg-contain {
	background-size: contain;
}
.bg-center {
	background-position: center;
}
.bg-right-top {
	background-position: right top;
}
.bg-left-top {
	background-position: left top;
}
.bg-right {
	background-position: right;
}
.bg-no-repeat {
	background-repeat: no-repeat;
}
.fill-\[\#D0AA49\] {
	fill: #D0AA49;
}
.fill-\[\#caa754\] {
	fill: #caa754;
}
.fill-current {
	fill: currentColor;
}
.p-0 {
	padding: 0px;
}
.p-5 {
	padding: 1.25rem;
}
.p-2 {
	padding: 0.5rem;
}
.p-4 {
	padding: 1rem;
}
.p-6 {
	padding: 1.5rem;
}
.p-8 {
	padding: 2rem;
}
.p-3 {
	padding: 0.75rem;
}
.p-10 {
	padding: 2.5rem;
}
.p-14 {
	padding: 3.5rem;
}
.p-1 {
	padding: 0.25rem;
}
.py-\[20px\] {
	padding-top: 20px;
	padding-bottom: 20px;
}
.py-3 {
	padding-top: 0.75rem;
	padding-bottom: 0.75rem;
}
.px-6 {
	padding-left: 1.5rem;
	padding-right: 1.5rem;
}
.px-2 {
	padding-left: 0.5rem;
	padding-right: 0.5rem;
}
.px-3 {
	padding-left: 0.75rem;
	padding-right: 0.75rem;
}
.py-2 {
	padding-top: 0.5rem;
	padding-bottom: 0.5rem;
}
.px-5 {
	padding-left: 1.25rem;
	padding-right: 1.25rem;
}
.px-8 {
	padding-left: 2rem;
	padding-right: 2rem;
}
.px-7 {
	padding-left: 1.75rem;
	padding-right: 1.75rem;
}
.py-4 {
	padding-top: 1rem;
	padding-bottom: 1rem;
}
.py-8 {
	padding-top: 2rem;
	padding-bottom: 2rem;
}
.px-1 {
	padding-left: 0.25rem;
	padding-right: 0.25rem;
}
.py-2\.5 {
	padding-top: 0.625rem;
	padding-bottom: 0.625rem;
}
.px-4 {
	padding-left: 1rem;
	padding-right: 1rem;
}
.px-0 {
	padding-left: 0px;
	padding-right: 0px;
}
.py-1 {
	padding-top: 0.25rem;
	padding-bottom: 0.25rem;
}
.px-10 {
	padding-left: 2.5rem;
	padding-right: 2.5rem;
}
.px-12 {
	padding-left: 3rem;
	padding-right: 3rem;
}
.py-16 {
	padding-top: 4rem;
	padding-bottom: 4rem;
}
.py-15 {
	padding-top: 60px;
	padding-bottom: 60px;
}
.px-15 {
	padding-left: 60px;
	padding-right: 60px;
}
.px-18 {
	padding-left: 77px;
	padding-right: 77px;
}
.py-5 {
	padding-top: 1.25rem;
	padding-bottom: 1.25rem;
}
.py-12 {
	padding-top: 3rem;
	padding-bottom: 3rem;
}
.py-3\.5 {
	padding-top: 0.875rem;
	padding-bottom: 0.875rem;
}
.px-16 {
	padding-left: 4rem;
	padding-right: 4rem;
}
.py-10 {
	padding-top: 2.5rem;
	padding-bottom: 2.5rem;
}
.py-24 {
	padding-top: 6rem;
	padding-bottom: 6rem;
}
.py-9 {
	padding-top: 2.25rem;
	padding-bottom: 2.25rem;
}
.\!px-5 {
	padding-left: 1.25rem !important;
	padding-right: 1.25rem !important;
}
.pt-4 {
	padding-top: 1rem;
}
.pl-3 {
	padding-left: 0.75rem;
}
.pb-4 {
	padding-bottom: 1rem;
}
.pt-5 {
	padding-top: 1.25rem;
}
.pb-0 {
	padding-bottom: 0px;
}
.pb-2 {
	padding-bottom: 0.5rem;
}
.pt-1 {
	padding-top: 0.25rem;
}
.pt-2 {
	padding-top: 0.5rem;
}
.pb-3 {
	padding-bottom: 0.75rem;
}
.pb-10 {
	padding-bottom: 2.5rem;
}
.pt-10 {
	padding-top: 2.5rem;
}
.pb-14 {
	padding-bottom: 3.5rem;
}
.pt-3 {
	padding-top: 0.75rem;
}
.pr-2 {
	padding-right: 0.5rem;
}
.pl-0 {
	padding-left: 0px;
}
.pl-2 {
	padding-left: 0.5rem;
}
.pb-6 {
	padding-bottom: 1.5rem;
}
.pr-3 {
	padding-right: 0.75rem;
}
.pt-12 {
	padding-top: 3rem;
}
.pb-1 {
	padding-bottom: 0.25rem;
}
.pb-52 {
	padding-bottom: 13rem;
}
.pb-8 {
	padding-bottom: 2rem;
}
.pb-\[60px\] {
	padding-bottom: 60px;
}
.pl-10 {
	padding-left: 2.5rem;
}
.pr-4 {
	padding-right: 1rem;
}
.pt-8 {
	padding-top: 2rem;
}
.pt-0 {
	padding-top: 0px;
}
.pl-15 {
	padding-left: 60px;
}
.pl-5 {
	padding-left: 1.25rem;
}
.pt-32 {
	padding-top: 8rem;
}
.pb-16 {
	padding-bottom: 4rem;
}
.pb-24 {
	padding-bottom: 6rem;
}
.pb-48 {
	padding-bottom: 12rem;
}
.pt-20 {
	padding-top: 5rem;
}
.pr-6 {
	padding-right: 1.5rem;
}
.\!pt-10 {
	padding-top: 2.5rem !important;
}
.pt-\[5\.5rem\] {
	padding-top: 5.5rem;
}
.pl-4 {
	padding-left: 1rem;
}
.\!pl-0 {
	padding-left: 0px !important;
}
.pt-24 {
	padding-top: 6rem;
}
.pb-12 {
	padding-bottom: 3rem;
}
.pl-1 {
	padding-left: 0.25rem;
}
.pt-6 {
	padding-top: 1.5rem;
}
.text-left {
	text-align: left;
}
.text-center {
	text-align: center;
}
.text-right {
	text-align: right;
}
.text-justify {
	text-align: justify;
}
.align-top {
	vertical-align: top;
}
.align-middle {
	vertical-align: middle;
}
.align-text-top {
	vertical-align: text-top;
}
.font-sans {
	font-family: ui-sans-serif, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
}
.text-3xl {
	font-size: 1.875rem;
	line-height: 2.25rem;
}
.text-lg {
	font-size: 1.125rem;
	line-height: 1.75rem;
}
.text-base {
	font-size: 1rem;
	line-height: 1.5rem;
}
.text-27p {
	font-size: 27px;
}
.text-xl {
	font-size: 1.25rem;
	line-height: 1.75rem;
}
.text-sm {
	font-size: 0.875rem;
	line-height: 1.25rem;
}
.text-xs {
	font-size: 0.75rem;
	line-height: 1rem;
}
.text-2xl {
	font-size: 1.5rem;
	line-height: 2rem;
}
.text-4xl {
	font-size: 2.25rem;
	line-height: 2.5rem;
}
.text-22p {
	font-size: 22px;
}
.\!text-\[35px\] {
	font-size: 35px !important;
}
.\!text-2xl {
	font-size: 1.5rem !important;
	line-height: 2rem !important;
}
.font-semibold {
	font-weight: 600;
}
.font-bold {
	font-weight: 700;
}
.font-light {
	font-weight: 300;
}
.font-medium {
	font-weight: 500;
}
.uppercase {
	text-transform: uppercase;
}
.italic {
	font-style: italic;
}
.not-italic {
	font-style: normal;
}
.leading-none {
	line-height: 1;
}
.leading-6 {
	line-height: 1.5rem;
}
.leading-9 {
	line-height: 2.25rem;
}
.leading-7 {
	line-height: 1.75rem;
}
.leading-5 {
	line-height: 1.25rem;
}
.leading-normal {
	line-height: 1.5;
}
.leading-tight {
	line-height: 1.25;
}
.tracking-wide {
	letter-spacing: 0.025em;
}
.tracking-normal {
	letter-spacing: 0em;
}
.tracking-wider {
	letter-spacing: 0.05em;
}
.tracking-tighter {
	letter-spacing: -0.05em;
}
.tracking-widest {
	letter-spacing: 0.1em;
}
.text-terra-khaki-green {
	--tw-text-opacity: 1;
	color: rgba(121, 145, 78, var(--tw-text-opacity));
}
.text-black {
	--tw-text-opacity: 1;
	color: rgba(0, 0, 0, var(--tw-text-opacity));
}
.text-terra-red {
	--tw-text-opacity: 1;
	color: rgba(216, 0, 0, var(--tw-text-opacity));
}
.text-white {
	--tw-text-opacity: 1;
	color: rgba(255, 255, 255, var(--tw-text-opacity));
}
.text-terra-via-gray {
	--tw-text-opacity: 1;
	color: rgba(116, 116, 116, var(--tw-text-opacity));
}
.text-terra-gray {
	--tw-text-opacity: 1;
	color: rgba(152, 152, 152, var(--tw-text-opacity));
}
.text-terra-text-gray {
	--tw-text-opacity: 1;
	color: rgba(103, 103, 103, var(--tw-text-opacity));
}
.text-\[\#F5993D\] {
	--tw-text-opacity: 1;
	color: rgba(245, 153, 61, var(--tw-text-opacity));
}
.text-terra-orange {
	--tw-text-opacity: 1;
	color: rgba(245, 153, 61, var(--tw-text-opacity));
}
.text-terra-dark-gray {
	--tw-text-opacity: 1;
	color: rgba(112, 112, 112, var(--tw-text-opacity));
}
.text-coupon-darkgreen {
	--tw-text-opacity: 1;
	color: rgba(44, 113, 15, var(--tw-text-opacity));
}
.text-terra-green {
	--tw-text-opacity: 1;
	color: rgba(37, 97, 48, var(--tw-text-opacity));
}
.text-terra-light-green {
	--tw-text-opacity: 1;
	color: rgba(132, 195, 72, var(--tw-text-opacity));
}
.text-lastgray {
	--tw-text-opacity: 1;
	color: rgba(170, 170, 170, var(--tw-text-opacity));
}
.text-terra-bg-soft-gray {
	--tw-text-opacity: 1;
	color: rgba(183, 183, 183, var(--tw-text-opacity));
}
.text-terra-other-green {
	--tw-text-opacity: 1;
	color: rgba(67, 125, 59, var(--tw-text-opacity));
}
.text-terra-other-gray-too {
	--tw-text-opacity: 1;
	color: rgba(155, 150, 150, var(--tw-text-opacity));
}
.text-terra-antrasit {
	--tw-text-opacity: 1;
	color: rgba(101, 101, 101, var(--tw-text-opacity));
}
.text-terra-mid-khaki-green {
	--tw-text-opacity: 1;
	color: rgba(129, 154, 83, var(--tw-text-opacity));
}
.text-\[\#79914E\] {
	--tw-text-opacity: 1;
	color: rgba(121, 145, 78, var(--tw-text-opacity));
}
.text-red-700 {
	--tw-text-opacity: 1;
	color: rgba(185, 28, 28, var(--tw-text-opacity));
}
.text-terra-antrasit-2 {
	--tw-text-opacity: 1;
	color: rgba(131, 131, 131, var(--tw-text-opacity));
}
.\!text-terra-red {
	--tw-text-opacity: 1 !important;
	color: rgba(216, 0, 0, var(--tw-text-opacity)) !important;
}
.text-terra-dark-antrasit {
	--tw-text-opacity: 1;
	color: rgba(86, 86, 86, var(--tw-text-opacity));
}
.text-terra-dark-khaki-green {
	--tw-text-opacity: 1;
	color: rgba(78, 76, 54, var(--tw-text-opacity));
}
.text-gray-200 {
	--tw-text-opacity: 1;
	color: rgba(229, 231, 235, var(--tw-text-opacity));
}
.text-gray-300 {
	--tw-text-opacity: 1;
	color: rgba(209, 213, 219, var(--tw-text-opacity));
}
.text-gray-400 {
	--tw-text-opacity: 1;
	color: rgba(156, 163, 175, var(--tw-text-opacity));
}
.text-gray-500 {
	--tw-text-opacity: 1;
	color: rgba(107, 114, 128, var(--tw-text-opacity));
}
.text-gray-600 {
	--tw-text-opacity: 1;
	color: rgba(75, 85, 99, var(--tw-text-opacity));
}
.text-gray-700 {
	--tw-text-opacity: 1;
	color: rgba(55, 65, 81, var(--tw-text-opacity));
}
.text-gray-900 {
	--tw-text-opacity: 1;
	color: rgba(17, 24, 39, var(--tw-text-opacity));
}
.text-gray-800 {
	--tw-text-opacity: 1;
	color: rgba(31, 41, 55, var(--tw-text-opacity));
}
.text-blue-600 {
	--tw-text-opacity: 1;
	color: rgba(37, 99, 235, var(--tw-text-opacity));
}
.text-milyem {
	--tw-text-opacity: 1;
	color: rgba(202, 167, 84, var(--tw-text-opacity));
}
.text-\[\#707070\] {
	--tw-text-opacity: 1;
	color: rgba(112, 112, 112, var(--tw-text-opacity));
}
.text-\[\#D0AA49\] {
	--tw-text-opacity: 1;
	color: rgba(208, 170, 73, var(--tw-text-opacity));
}
.text-\[\#caa754\] {
	--tw-text-opacity: 1;
	color: rgba(202, 167, 84, var(--tw-text-opacity));
}
.text-\[\#000000\] {
	--tw-text-opacity: 1;
	color: rgba(0, 0, 0, var(--tw-text-opacity));
}
.text-\[\#ffffff\] {
	--tw-text-opacity: 1;
	color: rgba(255, 255, 255, var(--tw-text-opacity));
}
.text-\[\#fba91a\] {
	--tw-text-opacity: 1;
	color: rgba(251, 169, 26, var(--tw-text-opacity));
}
.text-\[\#8472AB\] {
	--tw-text-opacity: 1;
	color: rgba(132, 114, 171, var(--tw-text-opacity));
}
.text-\[\#5AA5FC\] {
	--tw-text-opacity: 1;
	color: rgba(90, 165, 252, var(--tw-text-opacity));
}
.text-\[\#7EA391\] {
	--tw-text-opacity: 1;
	color: rgba(126, 163, 145, var(--tw-text-opacity));
}
.\!text-black {
	--tw-text-opacity: 1 !important;
	color: rgba(0, 0, 0, var(--tw-text-opacity)) !important;
}
.text-opacity-50 {
	--tw-text-opacity: 0.5;
}
.underline {
	text-decoration: underline;
}
.antialiased {
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}
.placeholder-transparent::-moz-placeholder {
	color: transparent;
}
.placeholder-transparent::placeholder {
	color: transparent;
}
.placeholder-black::-moz-placeholder {
	--tw-placeholder-opacity: 1;
	color: rgba(0, 0, 0, var(--tw-placeholder-opacity));
}
.placeholder-black::placeholder {
	--tw-placeholder-opacity: 1;
	color: rgba(0, 0, 0, var(--tw-placeholder-opacity));
}
.opacity-50 {
	opacity: 0.5;
}
.opacity-0 {
	opacity: 0;
}
.shadow-searchshadow {
	--tw-shadow: 0 8px 20px rgb(108 108 108 / 17%);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-aboutshadow {
	--tw-shadow: 0 0px 8px rgb(108 108 108 / 17%);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-institutional {
	--tw-shadow:  0 3px 4px rgb(0 0 0 / 16%);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow {
	--tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
	--tw-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
	--tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2xl {
	--tw-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none {
	outline: 2px solid transparent;
	outline-offset: 2px;
}
.ring-gray-300 {
	--tw-ring-opacity: 1;
	--tw-ring-color: rgba(209, 213, 219, var(--tw-ring-opacity));
}
.drop-shadow {
	--tw-drop-shadow: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1)) drop-shadow(0 1px 1px rgba(0, 0, 0, 0.06));
	filter: var(--tw-filter);
}
.drop-shadow-lg {
	--tw-drop-shadow: drop-shadow(0 10px 8px rgba(0, 0, 0, 0.04)) drop-shadow(0 4px 3px rgba(0, 0, 0, 0.1));
	filter: var(--tw-filter);
}
.drop-shadow-fundshadow {
	--tw-drop-shadow: drop-shadow(0px 3px 6px rgba(0, 0, 0, 0.16));
	filter: var(--tw-filter);
}
.drop-shadow-couponshadow {
	--tw-drop-shadow: drop-shadow(0px 0px 3px rgba(0, 0, 0, 0.2));
	filter: var(--tw-filter);
}
.grayscale {
	--tw-grayscale: grayscale(100%);
	filter: var(--tw-filter);
}
.filter {
	filter: var(--tw-filter);
}
.backdrop-blur {
	--tw-backdrop-blur: blur(8px);
	-webkit-backdrop-filter: var(--tw-backdrop-filter);
	        backdrop-filter: var(--tw-backdrop-filter);
}
.transition-all {
	transition-property: all;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: 150ms;
}
.transition {
	transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
	transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
	transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: 150ms;
}
.transition-shadow {
	transition-property: box-shadow;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: 150ms;
}
.transition-colors {
	transition-property: background-color, border-color, color, fill, stroke;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: 150ms;
}
.transition-transform {
	transition-property: transform;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: 150ms;
}
.delay-1000 {
	transition-delay: 1000ms;
}
.delay-100 {
	transition-delay: 100ms;
}
.duration-300 {
	transition-duration: 300ms;
}
.duration-150 {
	transition-duration: 150ms;
}
.duration-500 {
	transition-duration: 500ms;
}
.duration-700 {
	transition-duration: 700ms;
}
.ease-in-out {
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.marker\:h-1 *::marker {
	height: 0.25rem;
}
.marker\:text-4xl *::marker {
	font-size: 2.25rem;
	line-height: 2.5rem;
}
.marker\:text-\[\#F5993D\] *::marker {
	--tw-text-opacity: 1;
	color: rgba(245, 153, 61, var(--tw-text-opacity));
}
.marker\:h-1::marker {
	height: 0.25rem;
}
.marker\:text-4xl::marker {
	font-size: 2.25rem;
	line-height: 2.5rem;
}
.marker\:text-\[\#F5993D\]::marker {
	--tw-text-opacity: 1;
	color: rgba(245, 153, 61, var(--tw-text-opacity));
}
.before\:absolute::before {
	content: "";
	position: absolute;
}
.before\:-top-3::before {
	content: "";
	top: -0.75rem;
}
.before\:h-3::before {
	content: "";
	height: 0.75rem;
}
.before\:h-4::before {
	content: "";
	height: 1rem;
}
.before\:w-full::before {
	content: "";
	width: 100%;
}
.before\:w-4::before {
	content: "";
	width: 1rem;
}
.before\:bg-cover::before {
	content: "";
	background-size: cover;
}
.before\:bg-center::before {
	content: "";
	background-position: center;
}
.before\:bg-no-repeat::before {
	content: "";
	background-repeat: no-repeat;
}
.before\:opacity-0::before {
	content: "";
	opacity: 0;
}
.after\:absolute::after {
	content: "";
	position: absolute;
}
.after\:inset-0::after {
	content: "";
	top: 0px;
	right: 0px;
	bottom: 0px;
	left: 0px;
}
.after\:top-0\.5::after {
	content: "";
	top: 0.125rem;
}
.after\:left-\[2px\]::after {
	content: "";
	left: 2px;
}
.after\:top-0::after {
	content: "";
	top: 0px;
}
.after\:h-5::after {
	content: "";
	height: 1.25rem;
}
.after\:w-5::after {
	content: "";
	width: 1.25rem;
}
.after\:rounded-full::after {
	content: "";
	border-radius: 9999px;
}
.after\:border::after {
	content: "";
	border-width: 1px;
}
.after\:border-gray-300::after {
	content: "";
	--tw-border-opacity: 1;
	border-color: rgba(209, 213, 219, var(--tw-border-opacity));
}
.after\:bg-\[\#000000\]::after {
	content: "";
	--tw-bg-opacity: 1;
	background-color: rgba(0, 0, 0, var(--tw-bg-opacity));
}
.after\:bg-terra-soft-khaki-green::after {
	content: "";
	--tw-bg-opacity: 1;
	background-color: rgba(117, 139, 74, var(--tw-bg-opacity));
}
.after\:transition-all::after {
	content: "";
	transition-property: all;
	transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
	transition-duration: 150ms;
}
.after\:content-\[\'\'\]::after {
	content: '';
}
.checked\:bg-borderflat-gray:checked {
	--tw-bg-opacity: 1;
	background-color: rgba(198, 198, 198, var(--tw-bg-opacity));
}
.checked\:bg-\[\#D0AA49\]:checked {
	--tw-bg-opacity: 1;
	background-color: rgba(208, 170, 73, var(--tw-bg-opacity));
}
.hover\:border-terra-khaki-green:hover {
	--tw-border-opacity: 1;
	border-color: rgba(121, 145, 78, var(--tw-border-opacity));
}
.hover\:border-transparent:hover {
	border-color: transparent;
}
.hover\:bg-gray-100:hover {
	--tw-bg-opacity: 1;
	background-color: rgba(243, 244, 246, var(--tw-bg-opacity));
}
.hover\:bg-terra-light-soft-gray:hover {
	--tw-bg-opacity: 1;
	background-color: rgba(199, 214, 187, var(--tw-bg-opacity));
}
.hover\:bg-green-600:hover {
	--tw-bg-opacity: 1;
	background-color: rgba(5, 150, 105, var(--tw-bg-opacity));
}
.hover\:bg-red-400:hover {
	--tw-bg-opacity: 1;
	background-color: rgba(248, 113, 113, var(--tw-bg-opacity));
}
.hover\:bg-milyem:hover {
	--tw-bg-opacity: 1;
	background-color: rgba(202, 167, 84, var(--tw-bg-opacity));
}
.hover\:bg-\[\#caa754\]:hover {
	--tw-bg-opacity: 1;
	background-color: rgba(202, 167, 84, var(--tw-bg-opacity));
}
.hover\:bg-\[\#d0aa49ad\]:hover {
	background-color: #d0aa49ad;
}
.hover\:bg-\[\#fddca3\]:hover {
	--tw-bg-opacity: 1;
	background-color: rgba(253, 220, 163, var(--tw-bg-opacity));
}
.hover\:bg-opacity-50:hover {
	--tw-bg-opacity: 0.5;
}
.hover\:text-terra-khaki-green:hover {
	--tw-text-opacity: 1;
	color: rgba(121, 145, 78, var(--tw-text-opacity));
}
.hover\:text-white:hover {
	--tw-text-opacity: 1;
	color: rgba(255, 255, 255, var(--tw-text-opacity));
}
.hover\:text-blue-900:hover {
	--tw-text-opacity: 1;
	color: rgba(30, 58, 138, var(--tw-text-opacity));
}
.hover\:text-gray-500:hover {
	--tw-text-opacity: 1;
	color: rgba(107, 114, 128, var(--tw-text-opacity));
}
.hover\:text-gray-400:hover {
	--tw-text-opacity: 1;
	color: rgba(156, 163, 175, var(--tw-text-opacity));
}
.hover\:text-\[\#caa754\]:hover {
	--tw-text-opacity: 1;
	color: rgba(202, 167, 84, var(--tw-text-opacity));
}
.hover\:text-\[\#000000\]:hover {
	--tw-text-opacity: 1;
	color: rgba(0, 0, 0, var(--tw-text-opacity));
}
.hover\:text-black:hover {
	--tw-text-opacity: 1;
	color: rgba(0, 0, 0, var(--tw-text-opacity));
}
.hover\:text-\[\#D0AA49\]:hover {
	--tw-text-opacity: 1;
	color: rgba(208, 170, 73, var(--tw-text-opacity));
}
.hover\:underline:hover {
	text-decoration: underline;
}
.hover\:opacity-90:hover {
	opacity: 0.9;
}
.hover\:opacity-50:hover {
	opacity: 0.5;
}
.hover\:shadow-none:hover {
	--tw-shadow: 0 0 #0000;
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-lg:hover {
	--tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:shadow-xl:hover {
	--tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.hover\:ring-2:hover {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.hover\:ring-milyem\/10:hover {
	--tw-ring-color: rgba(202, 167, 84, 0.1);
}
.hover\:ring-\[\#D0AA49\]:hover {
	--tw-ring-opacity: 1;
	--tw-ring-color: rgba(208, 170, 73, var(--tw-ring-opacity));
}
.hover\:drop-shadow-purpleshadow:hover {
	--tw-drop-shadow: drop-shadow(0px 6px 12px rgba(132, 114, 171, 0.4));
	filter: var(--tw-filter);
}
.hover\:drop-shadow-blueshadow:hover {
	--tw-drop-shadow: drop-shadow(0px 6px 12px rgba(90, 165, 252, 0.4));
	filter: var(--tw-filter);
}
.hover\:drop-shadow-greenshadow:hover {
	--tw-drop-shadow: drop-shadow(0px 6px 12px rgba(126, 163, 145, 0.4));
	filter: var(--tw-filter);
}
.focus\:z-10:focus {
	z-index: 10;
}
.focus\:border-terra-flat-gray-2:focus {
	--tw-border-opacity: 1;
	border-color: rgba(208, 208, 208, var(--tw-border-opacity));
}
.focus\:border-black:focus {
	--tw-border-opacity: 1;
	border-color: rgba(0, 0, 0, var(--tw-border-opacity));
}
.focus\:border-blue-300:focus {
	--tw-border-opacity: 1;
	border-color: rgba(147, 197, 253, var(--tw-border-opacity));
}
.focus\:border-transparent:focus {
	border-color: transparent;
}
.focus\:bg-\[\#caa754\]:focus {
	--tw-bg-opacity: 1;
	background-color: rgba(202, 167, 84, var(--tw-bg-opacity));
}
.focus\:text-blue-900:focus {
	--tw-text-opacity: 1;
	color: rgba(30, 58, 138, var(--tw-text-opacity));
}
.focus\:text-\[\#caa754\]:focus {
	--tw-text-opacity: 1;
	color: rgba(202, 167, 84, var(--tw-text-opacity));
}
.focus\:underline:focus {
	text-decoration: underline;
}
.focus\:outline-none:focus {
	outline: 2px solid transparent;
	outline-offset: 2px;
}
.focus\:ring:focus {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-transparent:focus {
	--tw-ring-color: transparent;
}
.focus\:ring-terra-orange:focus {
	--tw-ring-opacity: 1;
	--tw-ring-color: rgba(245, 153, 61, var(--tw-ring-opacity));
}
.focus\:ring-terra-other-green:focus {
	--tw-ring-opacity: 1;
	--tw-ring-color: rgba(67, 125, 59, var(--tw-ring-opacity));
}
.focus\:ring-\[\#79914E\]:focus {
	--tw-ring-opacity: 1;
	--tw-ring-color: rgba(121, 145, 78, var(--tw-ring-opacity));
}
.focus\:ring-milyem:focus {
	--tw-ring-opacity: 1;
	--tw-ring-color: rgba(202, 167, 84, var(--tw-ring-opacity));
}
.focus\:ring-\[\#D0AA49\]:focus {
	--tw-ring-opacity: 1;
	--tw-ring-color: rgba(208, 170, 73, var(--tw-ring-opacity));
}
.focus\:ring-\[\#caa754\]:focus {
	--tw-ring-opacity: 1;
	--tw-ring-color: rgba(202, 167, 84, var(--tw-ring-opacity));
}
.focus\:ring-terra-khaki-green:focus {
	--tw-ring-opacity: 1;
	--tw-ring-color: rgba(121, 145, 78, var(--tw-ring-opacity));
}
.focus\:ring-offset-0:focus {
	--tw-ring-offset-width: 0px;
}
.active\:bg-gray-100:active {
	--tw-bg-opacity: 1;
	background-color: rgba(243, 244, 246, var(--tw-bg-opacity));
}
.active\:text-gray-700:active {
	--tw-text-opacity: 1;
	color: rgba(55, 65, 81, var(--tw-text-opacity));
}
.active\:text-gray-500:active {
	--tw-text-opacity: 1;
	color: rgba(107, 114, 128, var(--tw-text-opacity));
}
.group:focus-within .group-focus-within\:top-0 {
	top: 0px;
}
.group:focus-within .group-focus-within\:left-3 {
	left: 0.75rem;
}
.group:focus-within .group-focus-within\:-translate-y-full {
	--tw-translate-y: -100%;
	transform: var(--tw-transform);
}
.group:focus-within .group-focus-within\:px-1 {
	padding-left: 0.25rem;
	padding-right: 0.25rem;
}
.group:focus-within .group-focus-within\:pl-0 {
	padding-left: 0px;
}
.group:focus-within .group-focus-within\:text-base {
	font-size: 1rem;
	line-height: 1.5rem;
}
.group:focus-within .group-focus-within\:text-black {
	--tw-text-opacity: 1;
	color: rgba(0, 0, 0, var(--tw-text-opacity));
}
.group:hover .group-hover\:visible {
	visibility: visible;
}
.group:hover .group-hover\:scale-125 {
	--tw-scale-x: 1.25;
	--tw-scale-y: 1.25;
	transform: var(--tw-transform);
}
.group:hover .group-hover\:border-terra-orange {
	--tw-border-opacity: 1;
	border-color: rgba(245, 153, 61, var(--tw-border-opacity));
}
.group:hover .group-hover\:border-terra-other-green {
	--tw-border-opacity: 1;
	border-color: rgba(67, 125, 59, var(--tw-border-opacity));
}
.group:hover .group-hover\:from-\[\#000000b3\] {
	--tw-gradient-from: #000000b3;
	--tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(0, 0, 0, 0));
}
.group:hover .group-hover\:fill-\[\#D0AA49\] {
	fill: #D0AA49;
}
.group:hover .group-hover\:fill-\[\#caa754\] {
	fill: #caa754;
}
.group:hover .group-hover\:fill-\[\#256130\] {
	fill: #256130;
}
.group:hover .group-hover\:text-terra-orange {
	--tw-text-opacity: 1;
	color: rgba(245, 153, 61, var(--tw-text-opacity));
}
.group:hover .group-hover\:text-black {
	--tw-text-opacity: 1;
	color: rgba(0, 0, 0, var(--tw-text-opacity));
}
.group:hover .group-hover\:text-\[\#caa754\] {
	--tw-text-opacity: 1;
	color: rgba(202, 167, 84, var(--tw-text-opacity));
}
.group:hover .group-hover\:text-white {
	--tw-text-opacity: 1;
	color: rgba(255, 255, 255, var(--tw-text-opacity));
}
.group:hover .group-hover\:text-\[\#F5993D\] {
	--tw-text-opacity: 1;
	color: rgba(245, 153, 61, var(--tw-text-opacity));
}
.group:hover .group-hover\:text-terra-light-green {
	--tw-text-opacity: 1;
	color: rgba(132, 195, 72, var(--tw-text-opacity));
}
.group:hover .group-hover\:placeholder-white::-moz-placeholder {
	--tw-placeholder-opacity: 1;
	color: rgba(255, 255, 255, var(--tw-placeholder-opacity));
}
.group:hover .group-hover\:placeholder-white::placeholder {
	--tw-placeholder-opacity: 1;
	color: rgba(255, 255, 255, var(--tw-placeholder-opacity));
}
.group:hover .group-hover\:opacity-90 {
	opacity: 0.9;
}
.group:hover .group-hover\:opacity-100 {
	opacity: 1;
}
.group:hover .group-hover\:shadow-lg {
	--tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
	box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.group:hover .group-hover\:drop-shadow-lg {
	--tw-drop-shadow: drop-shadow(0 10px 8px rgba(0, 0, 0, 0.04)) drop-shadow(0 4px 3px rgba(0, 0, 0, 0.1));
	filter: var(--tw-filter);
}
.group:hover .group-hover\:drop-shadow-orangeshadow {
	--tw-drop-shadow: drop-shadow(0px 6px 12px rgba(245, 153, 51, 0.4));
	filter: var(--tw-filter);
}
.peer:checked ~ .peer-checked\:bg-\[\#D0E3BD\] {
	--tw-bg-opacity: 1;
	background-color: rgba(208, 227, 189, var(--tw-bg-opacity));
}
.peer:checked ~ .peer-checked\:bg-off-white {
	--tw-bg-opacity: 1;
	background-color: rgba(245, 245, 245, var(--tw-bg-opacity));
}
.peer:checked ~ .peer-checked\:after\:translate-x-full::after {
	content: "";
	--tw-translate-x: 100%;
	transform: var(--tw-transform);
}
.peer:checked ~ .peer-checked\:after\:border-terra-soft-khaki-green::after {
	content: "";
	--tw-border-opacity: 1;
	border-color: rgba(117, 139, 74, var(--tw-border-opacity));
}
.peer:-moz-placeholder-shown ~ .peer-placeholder-shown\:top-0 {
	top: 0px;
}
.peer:placeholder-shown ~ .peer-placeholder-shown\:top-0 {
	top: 0px;
}
.peer:-moz-placeholder-shown ~ .peer-placeholder-shown\:left-4 {
	left: 1rem;
}
.peer:placeholder-shown ~ .peer-placeholder-shown\:left-4 {
	left: 1rem;
}
.peer:-moz-placeholder-shown ~ .peer-placeholder-shown\:translate-y-0 {
	--tw-translate-y: 0px;
	transform: var(--tw-transform);
}
.peer:placeholder-shown ~ .peer-placeholder-shown\:translate-y-0 {
	--tw-translate-y: 0px;
	transform: var(--tw-transform);
}
.peer:-moz-placeholder-shown ~ .peer-placeholder-shown\:text-lg {
	font-size: 1.125rem;
	line-height: 1.75rem;
}
.peer:placeholder-shown ~ .peer-placeholder-shown\:text-lg {
	font-size: 1.125rem;
	line-height: 1.75rem;
}
.peer:valid ~ .peer-valid\:top-0 {
	top: 0px;
}
.peer:valid ~ .peer-valid\:-translate-y-full {
	--tw-translate-y: -100%;
	transform: var(--tw-transform);
}
.peer:valid ~ .peer-valid\:pl-0 {
	padding-left: 0px;
}
.peer:valid ~ .peer-valid\:text-base {
	font-size: 1rem;
	line-height: 1.5rem;
}
.peer:valid ~ .peer-valid\:text-black {
	--tw-text-opacity: 1;
	color: rgba(0, 0, 0, var(--tw-text-opacity));
}
.peer:focus ~ .peer-focus\:top-0 {
	top: 0px;
}
.peer:focus ~ .peer-focus\:left-4 {
	left: 1rem;
}
.peer:focus ~ .peer-focus\:-translate-y-full {
	--tw-translate-y: -100%;
	transform: var(--tw-transform);
}
.peer:focus ~ .peer-focus\:text-xs {
	font-size: 0.75rem;
	line-height: 1rem;
}
.peer:focus ~ .peer-focus\:text-black {
	--tw-text-opacity: 1;
	color: rgba(0, 0, 0, var(--tw-text-opacity));
}
.peer:focus ~ .peer-focus\:ring-4 {
	--tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
	--tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
	box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
@media (min-width: 640px) {

	.sm\:left-\[5\%\] {
		left: 5%;
	}

	.sm\:bottom-28 {
		bottom: 7rem;
	}

	.sm\:right-\[7\%\] {
		right: 7%;
	}

	.sm\:col-span-3 {
		grid-column: span 3 / span 3;
	}

	.sm\:flex {
		display: flex;
	}

	.sm\:hidden {
		display: none;
	}

	.sm\:w-7\/12 {
		width: 58.333333%;
	}

	.sm\:w-5\/12 {
		width: 41.666667%;
	}

	.sm\:flex-1 {
		flex: 1 1 0%;
	}

	.sm\:grid-cols-3 {
		grid-template-columns: repeat(3, minmax(0, 1fr));
	}

	.sm\:flex-row {
		flex-direction: row;
	}

	.sm\:flex-wrap {
		flex-wrap: wrap;
	}

	.sm\:items-center {
		align-items: center;
	}

	.sm\:justify-start {
		justify-content: flex-start;
	}

	.sm\:justify-between {
		justify-content: space-between;
	}

	.sm\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
		--tw-space-y-reverse: 0;
		margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
		margin-bottom: calc(0px * var(--tw-space-y-reverse));
	}

	.sm\:px-6 {
		padding-left: 1.5rem;
		padding-right: 1.5rem;
	}

	.sm\:pt-0 {
		padding-top: 0px;
	}

	.sm\:pr-4 {
		padding-right: 1rem;
	}

	.sm\:pl-4 {
		padding-left: 1rem;
	}

	.sm\:text-3xl {
		font-size: 1.875rem;
		line-height: 2.25rem;
	}

	.sm\:text-5xl {
		font-size: 3rem;
		line-height: 1;
	}
}
@media (min-width: 768px) {

	.md\:absolute {
		position: absolute;
	}

	.md\:right-0 {
		right: 0px;
	}

	.md\:-top-3 {
		top: -0.75rem;
	}

	.md\:left-20 {
		left: 5rem;
	}

	.md\:col-span-4 {
		grid-column: span 4 / span 4;
	}

	.md\:col-span-2 {
		grid-column: span 2 / span 2;
	}

	.md\:col-start-7 {
		grid-column-start: 7;
	}

	.md\:mt-0 {
		margin-top: 0px;
	}

	.md\:mb-0 {
		margin-bottom: 0px;
	}

	.md\:block {
		display: block;
	}

	.md\:flex {
		display: flex;
	}

	.md\:max-h-20 {
		max-height: 5rem;
	}

	.md\:w-96 {
		width: 24rem;
	}

	.md\:w-1\/2 {
		width: 50%;
	}

	.md\:w-4\/12 {
		width: 33.333333%;
	}

	.md\:w-8\/12 {
		width: 66.666667%;
	}

	.md\:w-6\/12 {
		width: 50%;
	}

	.md\:w-5\/12 {
		width: 41.666667%;
	}

	.md\:w-2\/5 {
		width: 40%;
	}

	.md\:w-3\/5 {
		width: 60%;
	}

	.md\:w-2\/4 {
		width: 50%;
	}

	.md\:w-full {
		width: 100%;
	}

	.md\:min-w-\[40vw\] {
		min-width: 40vw;
	}

	.md\:max-w-tablet {
		max-width: 90%;
	}

	.md\:max-w-3xl {
		max-width: 48rem;
	}

	.md\:max-w-lg {
		max-width: 32rem;
	}

	.md\:max-w-2xl {
		max-width: 42rem;
	}

	.md\:flex-shrink-0 {
		flex-shrink: 0;
	}

	.md\:flex-grow-0 {
		flex-grow: 0;
	}

	.md\:grid-cols-2 {
		grid-template-columns: repeat(2, minmax(0, 1fr));
	}

	.md\:grid-cols-4 {
		grid-template-columns: repeat(4, minmax(0, 1fr));
	}

	.md\:grid-cols-12 {
		grid-template-columns: repeat(12, minmax(0, 1fr));
	}

	.md\:grid-cols-3 {
		grid-template-columns: repeat(3, minmax(0, 1fr));
	}

	.md\:flex-row {
		flex-direction: row;
	}

	.md\:flex-wrap {
		flex-wrap: wrap;
	}

	.md\:space-x-7 > :not([hidden]) ~ :not([hidden]) {
		--tw-space-x-reverse: 0;
		margin-right: calc(1.75rem * var(--tw-space-x-reverse));
		margin-left: calc(1.75rem * calc(1 - var(--tw-space-x-reverse)));
	}

	.md\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
		--tw-space-y-reverse: 0;
		margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
		margin-bottom: calc(0px * var(--tw-space-y-reverse));
	}

	.md\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
		--tw-space-x-reverse: 0;
		margin-right: calc(1rem * var(--tw-space-x-reverse));
		margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
	}

	.md\:p-10 {
		padding: 2.5rem;
	}

	.md\:py-8 {
		padding-top: 2rem;
		padding-bottom: 2rem;
	}

	.md\:pr-5 {
		padding-right: 1.25rem;
	}

	.md\:pl-5 {
		padding-left: 1.25rem;
	}

	.md\:pl-2 {
		padding-left: 0.5rem;
	}

	.md\:pt-16 {
		padding-top: 4rem;
	}

	.md\:text-2xl {
		font-size: 1.5rem;
		line-height: 2rem;
	}

	.md\:text-base {
		font-size: 1rem;
		line-height: 1.5rem;
	}

	.md\:text-lg {
		font-size: 1.125rem;
		line-height: 1.75rem;
	}
}
@media (min-width: 1100px) {

	.lg\:container {
		width: 100%;
	}

	@media (min-width: 640px) {

		.lg\:container {
			max-width: 640px;
		}
	}

	@media (min-width: 710px) {

		.lg\:container {
			max-width: 710px;
		}
	}

	@media (min-width: 768px) {

		.lg\:container {
			max-width: 768px;
		}
	}

	@media (min-width: 1100px) {

		.lg\:container {
			max-width: 1100px;
		}
	}

	@media (min-width: 1280px) {

		.lg\:container {
			max-width: 1280px;
		}
	}

	@media (min-width: 1536px) {

		.lg\:container {
			max-width: 1536px;
		}
	}

	.lg\:visible {
		visibility: visible;
	}

	.lg\:invisible {
		visibility: hidden;
	}

	.lg\:absolute {
		position: absolute;
	}

	.lg\:relative {
		position: relative;
	}

	.lg\:inset-auto {
		top: auto;
		right: auto;
		bottom: auto;
		left: auto;
	}

	.lg\:top-\[12\%\] {
		top: 12%;
	}

	.lg\:-left-0 {
		left: 0px;
	}

	.lg\:-bottom-12 {
		bottom: -3rem;
	}

	.lg\:right-\[3\%\] {
		right: 3%;
	}

	.lg\:top-1\/4 {
		top: 25%;
	}

	.lg\:left-20 {
		left: 5rem;
	}

	.lg\:float-right {
		float: right;
	}

	.lg\:my-10 {
		margin-top: 2.5rem;
		margin-bottom: 2.5rem;
	}

	.lg\:mt-4 {
		margin-top: 1rem;
	}

	.lg\:mt-3 {
		margin-top: 0.75rem;
	}

	.lg\:mt-16 {
		margin-top: 4rem;
	}

	.lg\:mb-0 {
		margin-bottom: 0px;
	}

	.lg\:mb-12 {
		margin-bottom: 3rem;
	}

	.lg\:mb-6 {
		margin-bottom: 1.5rem;
	}

	.lg\:mr-0 {
		margin-right: 0px;
	}

	.lg\:-mt-40 {
		margin-top: -10rem;
	}

	.lg\:mt-2 {
		margin-top: 0.5rem;
	}

	.lg\:mt-0 {
		margin-top: 0px;
	}

	.lg\:ml-\[170px\] {
		margin-left: 170px;
	}

	.lg\:ml-5 {
		margin-left: 1.25rem;
	}

	.lg\:block {
		display: block;
	}

	.lg\:inline {
		display: inline;
	}

	.lg\:flex {
		display: flex;
	}

	.lg\:grid {
		display: grid;
	}

	.lg\:hidden {
		display: none;
	}

	.lg\:h-2\/3 {
		height: 66.666667%;
	}

	.lg\:h-1\/2 {
		height: 50%;
	}

	.lg\:h-\[40px\] {
		height: 40px;
	}

	.lg\:max-h-\[26px\] {
		max-height: 26px;
	}

	.lg\:w-2\/3 {
		width: 66.666667%;
	}

	.lg\:w-10\/12 {
		width: 83.333333%;
	}

	.lg\:w-1\/3 {
		width: 33.333333%;
	}

	.lg\:w-8\/12 {
		width: 66.666667%;
	}

	.lg\:w-4\/12 {
		width: 33.333333%;
	}

	.lg\:w-\[33\%\] {
		width: 33%;
	}

	.lg\:w-\[67\%\] {
		width: 67%;
	}

	.lg\:w-\[43\%\] {
		width: 43%;
	}

	.lg\:w-\[57\%\] {
		width: 57%;
	}

	.lg\:w-\[70\%\] {
		width: 70%;
	}

	.lg\:w-\[30\%\] {
		width: 30%;
	}

	.lg\:w-\[20\%\] {
		width: 20%;
	}

	.lg\:w-\[80\%\] {
		width: 80%;
	}

	.lg\:w-auto {
		width: auto;
	}

	.lg\:w-2\/6 {
		width: 33.333333%;
	}

	.lg\:w-4\/6 {
		width: 66.666667%;
	}

	.lg\:w-5\/12 {
		width: 41.666667%;
	}

	.lg\:w-3\/12 {
		width: 25%;
	}

	.lg\:w-\[40px\] {
		width: 40px;
	}

	.lg\:w-1\/2 {
		width: 50%;
	}

	.lg\:w-1\/12 {
		width: 8.333333%;
	}

	.lg\:w-1\/6 {
		width: 16.666667%;
	}

	.lg\:w-7\/12 {
		width: 58.333333%;
	}

	.lg\:w-3\/4 {
		width: 75%;
	}

	.lg\:min-w-min {
		min-width: -moz-min-content;
		min-width: min-content;
	}

	.lg\:max-w-1\/2 {
		max-width: 50%;
	}

	.lg\:max-w-5xl {
		max-width: 64rem;
	}

	.lg\:max-w-\[400px\] {
		max-width: 400px;
	}

	.lg\:max-w-70p {
		max-width: 70px;
	}

	.lg\:\!translate-y-4 {
		--tw-translate-y: 1rem !important;
		transform: var(--tw-transform) !important;
	}

	.lg\:-translate-y-32 {
		--tw-translate-y: -8rem;
		transform: var(--tw-transform);
	}

	.lg\:translate-y-4 {
		--tw-translate-y: 1rem;
		transform: var(--tw-transform);
	}

	.lg\:grid-cols-4 {
		grid-template-columns: repeat(4, minmax(0, 1fr));
	}

	.lg\:grid-cols-12 {
		grid-template-columns: repeat(12, minmax(0, 1fr));
	}

	.lg\:grid-cols-3 {
		grid-template-columns: repeat(3, minmax(0, 1fr));
	}

	.lg\:flex-row {
		flex-direction: row;
	}

	.lg\:\!flex-col {
		flex-direction: column !important;
	}

	.lg\:flex-nowrap {
		flex-wrap: nowrap;
	}

	.lg\:items-start {
		align-items: flex-start;
	}

	.lg\:items-end {
		align-items: flex-end;
	}

	.lg\:justify-start {
		justify-content: flex-start;
	}

	.lg\:justify-between {
		justify-content: space-between;
	}

	.lg\:gap-7 {
		gap: 1.75rem;
	}

	.lg\:gap-4 {
		gap: 1rem;
	}

	.lg\:space-y-0 > :not([hidden]) ~ :not([hidden]) {
		--tw-space-y-reverse: 0;
		margin-top: calc(0px * calc(1 - var(--tw-space-y-reverse)));
		margin-bottom: calc(0px * var(--tw-space-y-reverse));
	}

	.lg\:space-x-10 > :not([hidden]) ~ :not([hidden]) {
		--tw-space-x-reverse: 0;
		margin-right: calc(2.5rem * var(--tw-space-x-reverse));
		margin-left: calc(2.5rem * calc(1 - var(--tw-space-x-reverse)));
	}

	.lg\:space-x-20 > :not([hidden]) ~ :not([hidden]) {
		--tw-space-x-reverse: 0;
		margin-right: calc(5rem * var(--tw-space-x-reverse));
		margin-left: calc(5rem * calc(1 - var(--tw-space-x-reverse)));
	}

	.lg\:overflow-visible {
		overflow: visible;
	}

	.lg\:overflow-x-visible {
		overflow-x: visible;
	}

	.lg\:overflow-y-visible {
		overflow-y: visible;
	}

	.lg\:overflow-y-scroll {
		overflow-y: scroll;
	}

	.lg\:rounded-60p {
		border-radius: 60px;
	}

	.lg\:rounded-34p {
		border-radius: 34px;
	}

	.lg\:bg-transparent {
		background-color: transparent;
	}

	.lg\:via-transparent {
		--tw-gradient-stops: var(--tw-gradient-from), transparent, var(--tw-gradient-to, rgba(0, 0, 0, 0));
	}

	.lg\:p-4 {
		padding: 1rem;
	}

	.lg\:px-12 {
		padding-left: 3rem;
		padding-right: 3rem;
	}

	.lg\:px-15 {
		padding-left: 60px;
		padding-right: 60px;
	}

	.lg\:px-10 {
		padding-left: 2.5rem;
		padding-right: 2.5rem;
	}

	.lg\:px-8 {
		padding-left: 2rem;
		padding-right: 2rem;
	}

	.lg\:px-5 {
		padding-left: 1.25rem;
		padding-right: 1.25rem;
	}

	.lg\:py-4 {
		padding-top: 1rem;
		padding-bottom: 1rem;
	}

	.lg\:px-2 {
		padding-left: 0.5rem;
		padding-right: 0.5rem;
	}

	.lg\:px-6 {
		padding-left: 1.5rem;
		padding-right: 1.5rem;
	}

	.lg\:py-24 {
		padding-top: 6rem;
		padding-bottom: 6rem;
	}

	.lg\:px-24 {
		padding-left: 6rem;
		padding-right: 6rem;
	}

	.lg\:py-20 {
		padding-top: 5rem;
		padding-bottom: 5rem;
	}

	.lg\:py-3 {
		padding-top: 0.75rem;
		padding-bottom: 0.75rem;
	}

	.lg\:px-0 {
		padding-left: 0px;
		padding-right: 0px;
	}

	.lg\:pb-16 {
		padding-bottom: 4rem;
	}

	.lg\:pr-12 {
		padding-right: 3rem;
	}

	.lg\:pl-5 {
		padding-left: 1.25rem;
	}

	.lg\:pt-32 {
		padding-top: 8rem;
	}

	.lg\:pl-16 {
		padding-left: 4rem;
	}

	.lg\:pl-12 {
		padding-left: 3rem;
	}

	.lg\:pt-24 {
		padding-top: 6rem;
	}

	.lg\:pl-\[3\.75rem\] {
		padding-left: 3.75rem;
	}

	.lg\:pr-8 {
		padding-right: 2rem;
	}

	.lg\:pl-8 {
		padding-left: 2rem;
	}

	.lg\:\!pl-5 {
		padding-left: 1.25rem !important;
	}

	.lg\:pt-5 {
		padding-top: 1.25rem;
	}

	.lg\:pb-0 {
		padding-bottom: 0px;
	}

	.lg\:pl-6 {
		padding-left: 1.5rem;
	}

	.lg\:pb-\[60px\] {
		padding-bottom: 60px;
	}

	.lg\:pr-10 {
		padding-right: 2.5rem;
	}

	.lg\:text-center {
		text-align: center;
	}

	.lg\:text-right {
		text-align: right;
	}

	.lg\:text-base {
		font-size: 1rem;
		line-height: 1.5rem;
	}

	.lg\:text-lg {
		font-size: 1.125rem;
		line-height: 1.75rem;
	}

	.lg\:text-3xl {
		font-size: 1.875rem;
		line-height: 2.25rem;
	}

	.lg\:text-6xl {
		font-size: 3.75rem;
		line-height: 1;
	}

	.lg\:text-2xl {
		font-size: 1.5rem;
		line-height: 2rem;
	}

	.lg\:text-xl {
		font-size: 1.25rem;
		line-height: 1.75rem;
	}

	.lg\:text-4xl {
		font-size: 2.25rem;
		line-height: 2.5rem;
	}

	.lg\:leading-tight {
		line-height: 1.25;
	}

	.lg\:text-terra-khaki-green {
		--tw-text-opacity: 1;
		color: rgba(121, 145, 78, var(--tw-text-opacity));
	}

	.lg\:opacity-0 {
		opacity: 0;
	}

	.lg\:opacity-100 {
		opacity: 1;
	}

	.lg\:shadow-2xl {
		--tw-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
		box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
	}

	.lg\:shadow-advertisementshadow {
		--tw-shadow: 0px 4px 15px 4px rgb(94 94 94 / 61%);
		box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
	}

	.group:hover .lg\:group-hover\:translate-y-2 {
		--tw-translate-y: 0.5rem;
		transform: var(--tw-transform);
	}
}
@media (min-width: 1280px) {

	.xl\:w-10\/12 {
		width: 83.333333%;
	}

	.xl\:grid-cols-5 {
		grid-template-columns: repeat(5, minmax(0, 1fr));
	}

	.xl\:space-x-4 > :not([hidden]) ~ :not([hidden]) {
		--tw-space-x-reverse: 0;
		margin-right: calc(1rem * var(--tw-space-x-reverse));
		margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
	}

	.xl\:px-24 {
		padding-left: 6rem;
		padding-right: 6rem;
	}
}
@media (min-width: 1536px) {

	.\32xl\:left-\[8\%\] {
		left: 8%;
	}

	.\32xl\:bottom-8 {
		bottom: 2rem;
	}

	.\32xl\:mr-4 {
		margin-right: 1rem;
	}

	.\32xl\:\!max-w-7xl {
		max-width: 80rem !important;
	}

	.\32xl\:-translate-y-36 {
		--tw-translate-y: -9rem;
		transform: var(--tw-transform);
	}

	.\32xl\:grid-cols-5 {
		grid-template-columns: repeat(5, minmax(0, 1fr));
	}

	.\32xl\:p-6 {
		padding: 1.5rem;
	}

	.\32xl\:px-5 {
		padding-left: 1.25rem;
		padding-right: 1.25rem;
	}

	.\32xl\:pt-48 {
		padding-top: 12rem;
	}

	.\32xl\:pt-20 {
		padding-top: 5rem;
	}

	.\32xl\:text-xl {
		font-size: 1.25rem;
		line-height: 1.75rem;
	}

	.\32xl\:text-3xl {
		font-size: 1.875rem;
		line-height: 2.25rem;
	}
}
@media (min-width: 1280px) {

	.ts\:max-w-ts {
		max-width: 1112px;
	}

	.ts\:justify-center {
		justify-content: center;
	}
}
@media (min-width: 710px) {

	.mts\:mt-\[30px\] {
		margin-top: 30px;
	}

	.mts\:mt-0 {
		margin-top: 0px;
	}

	.mts\:mt-6 {
		margin-top: 1.5rem;
	}

	.mts\:w-3\/12 {
		width: 25%;
	}

	.mts\:w-9\/12 {
		width: 75%;
	}

	.mts\:py-\[50px\] {
		padding-top: 50px;
		padding-bottom: 50px;
	}
}
