/*! For license information please see countdown.bundle.js.LICENSE.txt */
!function(){var e={1093:function(e){!function(){"use strict";var t,n,s,i,r,o,u,a,d=1,c=2,l=4,m=8,h=16,f=32,y=64,w=128,p=256,g=512,M=1024,v=w|y|h|m|l|c,S=1e3,N=60,D=60,E=24,T=E*D*N*S,b=7,L=12,H=10,k=10,F=10,x=Math.ceil,j=Math.floor;function C(e,t){var n=e.getTime();return e.setMonth(e.getMonth()+t),Math.round((e.getTime()-n)/T)}function I(e){var t=e.getTime(),n=new Date(t);return n.setMonth(e.getMonth()+1),Math.round((n.getTime()-t)/T)}function O(e,t){if(t=t instanceof Date||null!==t&&isFinite(t)?new Date(+t):new Date,!e)return t;var n=+e.value||0;return n?(t.setTime(t.getTime()+n),t):((n=+e.milliseconds||0)&&t.setMilliseconds(t.getMilliseconds()+n),(n=+e.seconds||0)&&t.setSeconds(t.getSeconds()+n),(n=+e.minutes||0)&&t.setMinutes(t.getMinutes()+n),(n=+e.hours||0)&&t.setHours(t.getHours()+n),(n=+e.weeks||0)&&(n*=b),(n+=+e.days||0)&&t.setDate(t.getDate()+n),(n=+e.months||0)&&t.setMonth(t.getMonth()+n),(n=+e.millennia||0)&&(n*=F),(n+=+e.centuries||0)&&(n*=k),(n+=+e.decades||0)&&(n*=H),(n+=+e.years||0)&&t.setFullYear(t.getFullYear()+n),t)}function Y(e,s){return u(e)+(1===e?t[s]:n[s])}function A(){}function U(e,t,n,s,i,r){return e[n]>=0&&(t+=e[n],delete e[n]),1+(t/=i)<=1?0:e[s]>=0?(e[s]=+(e[s]+t).toFixed(r),function(e,t){switch(t){case"seconds":if(e.seconds!==N||isNaN(e.minutes))return;e.minutes++,e.seconds=0;case"minutes":if(e.minutes!==D||isNaN(e.hours))return;e.hours++,e.minutes=0;case"hours":if(e.hours!==E||isNaN(e.days))return;e.days++,e.hours=0;case"days":if(e.days!==b||isNaN(e.weeks))return;e.weeks++,e.days=0;case"weeks":if(e.weeks!==I(e.refMonth)/b||isNaN(e.months))return;e.months++,e.weeks=0;case"months":if(e.months!==L||isNaN(e.years))return;e.years++,e.months=0;case"years":if(e.years!==H||isNaN(e.decades))return;e.decades++,e.years=0;case"decades":if(e.decades!==k||isNaN(e.centuries))return;e.centuries++,e.decades=0;case"centuries":if(e.centuries!==F||isNaN(e.millennia))return;e.millennia++,e.centuries=0}}(e,s),0):t}function q(e,t,n,s,i,r){var o=new Date;if(e.start=t=t||o,e.end=n=n||o,e.units=s,e.value=n.getTime()-t.getTime(),e.value<0){var u=n;n=t,t=u}e.refMonth=new Date(t.getFullYear(),t.getMonth(),15,12,0,0);try{e.millennia=0,e.centuries=0,e.decades=0,e.years=n.getFullYear()-t.getFullYear(),e.months=n.getMonth()-t.getMonth(),e.weeks=0,e.days=n.getDate()-t.getDate(),e.hours=n.getHours()-t.getHours(),e.minutes=n.getMinutes()-t.getMinutes(),e.seconds=n.getSeconds()-t.getSeconds(),e.milliseconds=n.getMilliseconds()-t.getMilliseconds(),function(e){var t;for(e.milliseconds<0?(t=x(-e.milliseconds/S),e.seconds-=t,e.milliseconds+=t*S):e.milliseconds>=S&&(e.seconds+=j(e.milliseconds/S),e.milliseconds%=S),e.seconds<0?(t=x(-e.seconds/N),e.minutes-=t,e.seconds+=t*N):e.seconds>=N&&(e.minutes+=j(e.seconds/N),e.seconds%=N),e.minutes<0?(t=x(-e.minutes/D),e.hours-=t,e.minutes+=t*D):e.minutes>=D&&(e.hours+=j(e.minutes/D),e.minutes%=D),e.hours<0?(t=x(-e.hours/E),e.days-=t,e.hours+=t*E):e.hours>=E&&(e.days+=j(e.hours/E),e.hours%=E);e.days<0;)e.months--,e.days+=C(e.refMonth,1);e.days>=b&&(e.weeks+=j(e.days/b),e.days%=b),e.months<0?(t=x(-e.months/L),e.years-=t,e.months+=t*L):e.months>=L&&(e.years+=j(e.months/L),e.months%=L),e.years>=H&&(e.decades+=j(e.years/H),e.years%=H,e.decades>=k&&(e.centuries+=j(e.decades/k),e.decades%=k,e.centuries>=F&&(e.millennia+=j(e.centuries/F),e.centuries%=F)))}(e),function(e,t,n,s){var i=0;!(t&M)||i>=n?(e.centuries+=e.millennia*F,delete e.millennia):e.millennia&&i++,!(t&g)||i>=n?(e.decades+=e.centuries*k,delete e.centuries):e.centuries&&i++,!(t&p)||i>=n?(e.years+=e.decades*H,delete e.decades):e.decades&&i++,!(t&w)||i>=n?(e.months+=e.years*L,delete e.years):e.years&&i++,!(t&y)||i>=n?(e.months&&(e.days+=C(e.refMonth,e.months)),delete e.months,e.days>=b&&(e.weeks+=j(e.days/b),e.days%=b)):e.months&&i++,!(t&f)||i>=n?(e.days+=e.weeks*b,delete e.weeks):e.weeks&&i++,!(t&h)||i>=n?(e.hours+=e.days*E,delete e.days):e.days&&i++,!(t&m)||i>=n?(e.minutes+=e.hours*D,delete e.hours):e.hours&&i++,!(t&l)||i>=n?(e.seconds+=e.minutes*N,delete e.minutes):e.minutes&&i++,!(t&c)||i>=n?(e.milliseconds+=e.seconds*S,delete e.seconds):e.seconds&&i++,t&d&&!(i>=n)||function(e,t){var n,s,i,r=U(e,0,"milliseconds","seconds",S,t);if(r&&(r=U(e,r,"seconds","minutes",N,t))&&(r=U(e,r,"minutes","hours",D,t))&&(r=U(e,r,"hours","days",E,t))&&(r=U(e,r,"days","weeks",b,t))&&(r=U(e,r,"weeks","months",I(e.refMonth)/b,t))&&(r=U(e,r,"months","years",(s=(n=e.refMonth).getTime(),(i=new Date(s)).setFullYear(n.getFullYear()+1),Math.round((i.getTime()-s)/T)/I(e.refMonth)),t))&&(r=U(e,r,"years","decades",H,t))&&(r=U(e,r,"decades","centuries",k,t))&&(r=U(e,r,"centuries","millennia",F,t)))throw new Error("Fractional unit overflow")}(e,s)}(e,s,i,r)}finally{delete e.refMonth}return e}function R(e,t,n,s,i){var r;n=+n||v,s=s>0?s:NaN,i=i>0?i<20?Math.round(i):20:0;var o=null;"function"==typeof e?(r=e,e=null):e instanceof Date||(null!==e&&isFinite(e)?e=new Date(+e):("object"==typeof o&&(o=e),e=null));var u=null;if("function"==typeof t?(r=t,t=null):t instanceof Date||(null!==t&&isFinite(t)?t=new Date(+t):("object"==typeof t&&(u=t),t=null)),o&&(e=O(o,t)),u&&(t=O(u,e)),!e&&!t)return new A;if(!r)return q(new A,e,t,n,s,i);var a,f=function(e){return e&d?S/30:e&c?S:e&l?S*N:e&m?S*N*D:e&h?S*N*D*E:S*N*D*E*b}(n),y=function(){r(q(new A,e,t,n,s,i),a)};return y(),a=setInterval(y,f)}A.prototype.toString=function(e){var t=a(this),n=t.length;if(!n)return e?""+e:r;if(1===n)return t[0];var o=s+t.pop();return t.join(i)+o},A.prototype.toHTML=function(e,t){e=e||"span";var n=a(this),o=n.length;if(!o)return(t=t||r)?"<"+e+">"+t+"</"+e+">":t;for(var u=0;u<o;u++)n[u]="<"+e+">"+n[u]+"</"+e+">";if(1===o)return n[0];var d=s+n.pop();return n.join(i)+d},A.prototype.addTo=function(e){return O(this,e)},a=function(e){var t=[],n=e.millennia;return n&&t.push(o(n,10)),(n=e.centuries)&&t.push(o(n,9)),(n=e.decades)&&t.push(o(n,8)),(n=e.years)&&t.push(o(n,7)),(n=e.months)&&t.push(o(n,6)),(n=e.weeks)&&t.push(o(n,5)),(n=e.days)&&t.push(o(n,4)),(n=e.hours)&&t.push(o(n,3)),(n=e.minutes)&&t.push(o(n,2)),(n=e.seconds)&&t.push(o(n,1)),(n=e.milliseconds)&&t.push(o(n,0)),t},R.MILLISECONDS=d,R.SECONDS=c,R.MINUTES=l,R.HOURS=m,R.DAYS=h,R.WEEKS=f,R.MONTHS=y,R.YEARS=w,R.DECADES=p,R.CENTURIES=g,R.MILLENNIA=M,R.DEFAULTS=v,R.ALL=M|g|p|w|y|f|h|m|l|c|d;var P=R.setFormat=function(e){if(e){if("singular"in e||"plural"in e){var a=e.singular||[];a.split&&(a=a.split("|"));var d=e.plural||[];d.split&&(d=d.split("|"));for(var c=0;c<=10;c++)t[c]=a[c]||t[c],n[c]=d[c]||n[c]}"string"==typeof e.last&&(s=e.last),"string"==typeof e.delim&&(i=e.delim),"string"==typeof e.empty&&(r=e.empty),"function"==typeof e.formatNumber&&(u=e.formatNumber),"function"==typeof e.formatter&&(o=e.formatter)}},_=R.resetFormat=function(){t=" millisecond| second| minute| hour| day| week| month| year| decade| century| millennium".split("|"),n=" milliseconds| seconds| minutes| hours| days| weeks| months| years| decades| centuries| millennia".split("|"),s=" and ",i=", ",r="",u=function(e){return e},o=Y};R.setLabels=function(e,t,n,s,i,r,o){P({singular:e,plural:t,last:n,delim:s,empty:i,formatNumber:r,formatter:o})},R.resetLabels=_,_(),e.exports?e.exports=R:"undefined"!=typeof window&&"function"==typeof window.define&&void 0!==window.define.amd&&window.define("countdown",[],(function(){return R}))}()}},t={};function n(s){var i=t[s];if(void 0!==i)return i.exports;var r=t[s]={exports:{}};return e[s](r,r.exports,n),r.exports}n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,{a:t}),t},n.d=function(e,t){for(var s in t)n.o(t,s)&&!n.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},function(){"use strict";var e=n(1093),t=n.n(e);new class{constructor(){this.timers=document.querySelectorAll(".js-countdown-timer"),this.timer=document.querySelector(".js-countdown-single-timer"),this.timerDays=document.querySelector(".js-countdown-days-number"),this.timerHours=document.querySelector(".js-countdown-hours-number"),this.timerMinutes=document.querySelector(".js-countdown-minutes-number"),this.timerSeconds=document.querySelector(".js-countdown-seconds-number"),this.timerEndsLabel=document.querySelector(".js-countdown-ends-label"),this.initCountdown(),this.initCountdownSingle()}format(e){return e<10?"0"+e:e}initCountdownSingle(){if(!this.timer)return;const e=this.timer.dataset.countdown.toString();var n=t()(new Date(e),(e=>{e.value>0?(this.timer.innerHTML=this.timer.dataset.expired,this.timerEndsLabel.remove(),window.clearInterval(n)):(this.timerDays.innerHTML=this.format(e.days),this.timerHours.innerHTML=this.format(e.hours),this.timerMinutes.innerHTML=this.format(e.minutes),this.timerSeconds.innerHTML=this.format(e.seconds))}),t().DAYS|t().HOURS|t().MINUTES|t().SECONDS)}initCountdown(){this.timers&&this.timers.forEach((e=>{const n=e.dataset.countdown.toString();var s=t()(new Date(n),(t=>{const n=t.days?this.format(t.days)+" : ":"",i=t.hours|t.days?this.format(t.hours)+" : ":"";if(t.value>0)e.innerHTML=e.dataset.expired,e.nextElementSibling&&e.nextElementSibling.remove(),e.previousElementSibling&&e.previousElementSibling.remove(),window.clearInterval(s);else{const s=e.nextElementSibling.dataset.countdownleft;e.innerHTML=n+i+this.format(t.minutes)+" : "+this.format(t.seconds),e.nextElementSibling.textContent=s}}),t().DAYS|t().HOURS|t().MINUTES|t().SECONDS)}))}}}()}();