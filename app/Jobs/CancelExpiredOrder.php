<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Webkul\Sales\Models\Order;
use Webkul\Sales\Repositories\OrderRepository;

class CancelExpiredOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 6;

    public int $backoff = 30;

    public int $timeout = 120;

    public function __construct(
        protected $orderId
    ) {
        //
    }

    public function handle()
    {
        $order           = Order::find($this->orderId);
        $orderRepository = app(OrderRepository::class);

        if ($order && $order->status == 'pending') {
            $orderPayment = $order->payment;
            if ($orderPayment && $orderPayment->method == 'moneytransfer' && $orderPayment->created_at->diffInHours(now()) >= 24) {
                $orderRepository->cancel($this->orderId);
            }
        }
    }

    public function failed($exception)
    {
        Log::channel('cronjob')
            ->info('Failed to cancel expired order', [
                'orderId'   => $this->orderId,
                'exception' => $exception->getMessage(),
            ]);
    }
}
