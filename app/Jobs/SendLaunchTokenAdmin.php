<?php

namespace App\Jobs;

use App\Mail\LaunchTokenErrorNotification;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;
use Webkul\Sales\Models\Order;

class SendLaunchTokenAdmin implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private Order $order;

    private $lockToken;

    private $error;

    private $email;

    public function __construct(
        $order,
        $lockToken,
        $error
    ) {
        $this->order     = $order;
        $this->lockToken = $lockToken;
        $this->error     = $error;
        $this->email     = $order->customer_email;
    }

    public function handle()
    {
        Mail::to($this->email)->send(new LaunchTokenErrorNotification($this->error, $this->lockToken, $this->order));
    }
}
