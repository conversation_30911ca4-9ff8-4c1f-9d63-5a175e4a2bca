<?php

namespace App\Http\Controllers;

use App\Http\Requests\SubscriberRequest;
use App\SubscriberMail;
use Illuminate\Support\Facades\Mail;
use Webkul\Customer\Notifications\CustomerSubscription;
use Webkul\Customer\Notifications\CustomerSubscriptionCompleted;

class HomeController extends Controller
{
    public function SubscriberForm(SubscriberRequest $request)
    {
        $email = request()->input('email');

        $unique            = 0;
        $alreadySubscribed = SubscriberMail::where(['email' => $email])->get();

        $unique = function () use ($alreadySubscribed) {
            return ! $alreadySubscribed->count();
        };

        if (true) {
            $token = uniqid();

            $subscription = SubscriberMail::create([
                'kvkk'  => $request->kvkk,
                'email' => $request->email,
                'token' => uniqid(),
            ]);

            session()->flash('success', __('velocity::app-static.homepage.subscribe-ok'));

            Mail::queue(new CustomerSubscription($request->email, $subscription->token));
        }

        return redirect('/');
    }

    public function Unsubscribe($token)
    {
        $subscriber = SubscriberMail::where('token', $token)->first();

        if ($subscriber) {
            $subscriber->update([
                'is_subscriber'   => false,
                'unsubscribed_at' => now(),
            ]);

            session()->flash('success', __('velocity::app-static.homepage.unsubscribe-ok'));
        } else {
            session()->flash('error', __('velocity::app-static.homepage.unsubscribe-fail'));
        }

        return redirect('/');
    }

    public function Subscribe()
    {
        $token = request('token');

        $subscriber = SubscriberMail::where('token', $token)->first();

        if ($subscriber) {
            $subscriber->update([
                'is_subscriber' => true,
                'subscribed_at' => now(),
            ]);

            Mail::queue(new CustomerSubscriptionCompleted($subscriber->email, $subscriber->token));

            session()->flash('success', __('velocity::app-static.homepage.subscribe-ok'));
        }

        return redirect('/');
    }
}
